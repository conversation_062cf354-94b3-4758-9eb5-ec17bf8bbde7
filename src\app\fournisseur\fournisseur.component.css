.welcome-header {
  background: linear-gradient(to right, #2563eb, #1e40af); /* bleu dégradé */
  color: white;
  padding: 30px 40px;
  border-radius: 12px;
  margin-bottom: 20px;
}

.welcome-header h1 {
  font-size: 28px;
  font-weight: bold;
  margin: 0 0 8px 0;
  color: white;
}

.welcome-header p {
  font-size: 16px;
  margin: 0;
  opacity: 0.9;
}
.stats-container {
  display: flex;
  justify-content: space-around;
  flex-wrap: wrap;
  padding: 20px;
  gap: 20px;
}

.stat-card {
  background-color: #ffffff;
  border-radius: 15px;
  box-shadow: 0 0 15px rgba(0, 0, 0, 0.05);
  padding: 20px 30px;
  text-align: center;
  flex: 1 1 200px;
  max-width: 250px;
  transition: transform 0.2s ease;
}

.stat-card:hover {
  transform: translateY(-5px);
}

.stat-label {
  font-size: 14px;
  color: #000000; /* gris foncé */
  margin-bottom: 10px;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #111827; /* noir bleuté */
}
.header-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #f9fafb; /* fond très clair */
  padding: 20px 30px;
  border-radius: 10px;
  margin-bottom: 20px;
}

.header-text h2 {
  margin: 0;
  font-size: 28px;
  color: #111827; /* noir bleuté */
  font-weight: 700;
}

.header-text p {
  margin: 5px 0 0 0;
  color: #6b7280; /* gris foncé */
  font-size: 14px;
}

.add-user-btn {
  background-color: #0051ff; /* bouton noir bleuté */
  color: white;
  padding: 10px 16px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  transition: background-color 0.2s ease;
}

.add-user-btn:hover {
  background-color: #1f2937;
}

.add-user-btn .icon {
  margin-right: 8px;
  font-size: 16px;
}
/* Modal background */
.modal {
  display: none; /* caché par défaut */
  position: fixed;
  z-index: 1000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  overflow: auto;
  background-color: rgba(0,0,0,0.4); /* fond semi-transparent */
}

/* Modal box */
.modal-content {
  background-color: #fff;
  margin: 10% auto;
  padding: 20px;
  border-radius: 10px;
  width: 400px;
  max-width: 90%;
  box-shadow: 0 5px 15px rgba(0,0,0,0.3);
  position: relative;
  animation: fadeIn 0.3s ease;
}

/* Animation */
@keyframes fadeIn {
  from {opacity: 0;}
  to {opacity: 1;}
}

/* Close button */
.close {
  color: #aaa;
  position: absolute;
  top: 12px;
  right: 16px;
  font-size: 28px;
  font-weight: bold;
  cursor: pointer;
}

.close:hover {
  color: #000;
}

/* Form styles */
.modal-content input {
  width: 100%;
  margin: 10px 0;
  padding: 10px;
  border-radius: 6px;
  border: 10px solid #000000;

}

.modal-content button[type="submit"] {
  background-color: #111827;
  color: white;
  border: none;
  padding: 10px 15px;
  border-radius: 6px;
  cursor: pointer;
}

.modal-content button[type="submit"]:hover {
  background-color: #1f2937;
}
.modal {
  display: none;
  position: fixed;
  z-index: 1000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.4);
  justify-content: center;
  align-items: center;
}

.modal.show {
  display: flex;
}

.modal-content {
  background-color: white;
  padding: 20px;
  border-radius: 10px;
  width: 400px;
  max-width: 90%;
  position: relative;
}

.close {
  position: absolute;
  top: 10px;
  right: 15px;
  font-size: 24px;
  cursor: pointer;
}
.stats-container {
  display: flex;
  justify-content: space-around;
  flex-wrap: wrap;
  padding: 20px;
  gap: 20px;

  /* ↓↓↓ ajoute cette ligne ↓↓↓ */
  margin-top: -30px; /* ajuste à ta convenance */
}
select {
  width: 100%;
  max-width: 400px; /* adapte selon besoin */
  padding: 10px 15px;
  border: 1.5px solid #ccc;
  border-radius: 6px;
  background-color: #fff;
  font-size: 1rem;
  color: #333;
  appearance: none; /* enlève style par défaut navigateur */
  cursor: pointer;
  transition: border-color 0.3s ease;
}

/* Ajouter une petite flèche personnalisée */
select {
  background-image: url("data:image/svg+xml;charset=US-ASCII,%3Csvg width='14' height='8' viewBox='0 0 14 8' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath fill='%23333' d='M7 8L0 0h14L7 8z'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 15px center;
  background-size: 14px 8px;
  margin-bottom: 20px; /* espace sous chaque select */
}

/* Au focus, changer la bordure */
select:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 5px rgba(0,123,255,0.5);
}

/* Survol */
select:hover {
  border-color: #888;
}
/* Boîte blanche autour du champ */
.search-wrapper {
  background-color: #ffffff;
  padding: 20px;
  border-radius: 12px;
  border: 1px solid #eee;
  max-width: 1200px;
  margin: 0 auto; /* centre la boîte si besoin */
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05); /* ombre douce */
  margin-top: -20px;
}

/* Champ de recherche */
.custom-search {
  position: relative;
  width: 100%;
}

.custom-search input {
  width: 100%;
  padding: 10px 12px 10px 40px;
  border: 1px solid #dcdcdc;
  border-radius: 8px;
  background-color: #fff;
  font-size: 14px;
  color: #333;
  outline: none;
  box-sizing: border-box;
  transition: border-color 0.2s ease;
}

.custom-search input:focus {
  border-color: #bbb;
}

/* Icône loupe à gauche */
.icon-search {
  position: absolute;
  top: 50%;
  left: 12px;
  transform: translateY(-50%);
  width: 16px;
  height: 16px;
  background-image: url('data:image/svg+xml;utf8,<svg fill="%23999" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path d="M505 442.7L405.3 343c28.4-34.9 45.7-79 45.7-127C451 103.5 349.5 2 225.5 2S0 103.5 0 216.5 103.5 431 216.5 431c48 0 92.1-17.3 127-45.7l99.7 99.7c4.6 4.6 10.6 7 16.7 7s12.1-2.3 16.7-7c9.3-9.3 9.3-24.4 0-33.7zM216.5 366c-82.6 0-150-67.4-150-150s67.4-150 150-150 150 67.4 150 150-67.4 150-150 150z"/></svg>');
  background-repeat: no-repeat;
  background-size: 16px 16px;
  pointer-events: none;
}
/* Modal background */
.modal {
  position: fixed;
  top: 0; left: 0; right: 0; bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 999;
  visibility: hidden;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.modal.show {
  visibility: visible;
  opacity: 1;
}

/* Modal content box */
.modal-content {
  background: white;
  padding: 24px;
  border-radius: 12px;
  width: 500px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  font-family: 'Segoe UI', sans-serif;
  position: relative;
}

/* Close button */
.close {
  position: absolute;
  top: 16px;
  right: 16px;
  font-size: 20px;
  cursor: pointer;
  color: #6b7280;
}

/* Form inputs */
.modal-content input,
.modal-content select {
  width: 100%;
  padding: 10px 12px;
  margin-top: 10px;
  margin-bottom: 6px;
  font-size: 14px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  outline: none;
  font-family: inherit;
}

.modal-content input:focus,
.modal-content select:focus {
  border-color: #2563eb;
  box-shadow: 0 0 0 1px #2563eb;
}

/* Labels are hidden so we use placeholders instead (like your design) */

/* Submit button */
.modal-content button[type="submit"] {
  background-color: #2563eb;
  color: white;
  padding: 10px 20px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  margin-top: 16px;
  cursor: pointer;
  float: right;
}

/* Error messages */
.modal-content div[style*="color:red"] {
  font-size: 13px;
  margin-top: -4px;
  margin-bottom: 8px;
}
textarea {
  
  border-radius: 8px;         /* ⬅️ This gives it curved edges */
  border: 1px solid #d1d5db;
  padding: 10px 12px;
  font-size: 14px;
  font-family: inherit;
  outline: none;
  width: 100%;
  resize: vertical;           /* optional: allow resizing vertically only */
}

textarea:focus {
  border-color: #000000;
  box-shadow: 0 0 0 1px #2563eb;
  border: 3px solid black;
}


.modal .form-inputp {
  border: 2px solid #d1d5db;
  border-radius: 8px;
  padding: 10px 12px;
  font-size: 14px;
  width: 100%;
  margin-top: 5px;
  margin-bottom: 10px;
  outline: none;
  font-family: inherit;
}

.modal .form-inputp:focus {
  border-color: #000000;
  box-shadow: 0 0 0 1px #000000;
}
* {
  box-sizing: border-box;
  font-family: 'Inter', sans-serif;
  margin: 0;
  padding: 0;
}

body {
  background-color: #f6f7fb;
  padding: 30px;
}

.container {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  margin-left: -30px;
  width: 1300px; /* or 100vw if you want full screen width */
}

.card {
  width: 570px; /* as big as you want now! */
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  padding: 20px;
  box-sizing: border-box;
}



.header {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  position: relative;
}

.icon img {
  width: 32px;
  height: 32px;
  background-color: #e7f8ed;
  padding: 6px;
  border-radius: 8px;
}

.info {
  margin-left: 10px;
}

.info h2 {
  font-size: 18px;
  font-weight: 600;
}

.info p {
  font-size: 14px;
  color: #777;
}

.status {
  background-color: #e0f7e9;
  color: #28a745;
  font-size: 12px;
  padding: 3px 10px;
  border-radius: 12px;
  position: absolute;
  right: 0;
  top: 0;
}

.details {
  margin-top: -12px; /* lift the entire group upward */
}

.details p {
  font-size: 14px;
  margin-top: 13px; /* space between lines */
  color: #666666;
}

.actions {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-top: 15px;
}

.view {
  background-color: #ffffff;
    width: 500px; /* largeur augmentée ici */
  height: 37px;
  padding: 10px 24px;
  border: 1px solid #ccc;
  border-radius: 10px;
  font-size: 14px;
  cursor: pointer;
  background-color: #fff;
  color: #000;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;

}

.edit {
  background-color: #ffffff;
    width: 500px; /* largeur augmentée ici */
  height: 37px;
  padding: 10px 24px;
  border: 1px solid #ccc;
  border-radius: 10px;
  font-size: 14px;
  cursor: pointer;
  background-color: #fff;
  color: #000;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;

}



.simple-notification {
  width: 100%;
  padding: 12px 20px;
  margin-bottom: 15px;
  text-align: center;
  font-weight: 500;
  font-size: 14px;
  border-radius: 8px;
  
}

.simple-notification.success {
  background-color: #d4edda;
  color: #153257;
  border: 1px solid #c3e6cb;
}

.simple-notification.error {
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

/* Styles pour le tableau des fournisseurs */
.varient-table {
  border-collapse: separate;
  border-spacing: 0;
}

.varient-table thead th {
  background-color: #f8f9fa;
  border-bottom: 2px solid #dee2e6;
  font-weight: 600;
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  padding: 1rem 0.5rem;
}

.varient-table tbody tr {
  border-bottom: 1px solid #eee;
  transition: background-color 0.2s ease;
}

.varient-table tbody tr:hover {
  background-color: #f8f9fa;
}

.varient-table tbody td {
  padding: 1rem 0.5rem;
  vertical-align: middle;
}

.varient-table .btn-sm {
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
  border-radius: 0.375rem;
}

.text-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* Amélioration de l'affichage du tableau */
.table-responsive {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 0 10px rgba(0,0,0,0.1);
}

.varient-table tbody td img {
  border: 2px solid #e9ecef;
}

/* Styles spécifiques pour les icônes dans le tableau */
.varient-table tbody td img[src*="mail.JPG"],
.varient-table tbody td img[src*="phone.JPG"],
.varient-table tbody td img[src*="map.JPG"] {
  border-radius: 50%;
  border: 1px solid #dee2e6;
}

/* Override des styles de container pour le tableau */
.container-fluid .card {
  width: 100%;
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  padding: 0;
  box-sizing: border-box;
}

/* Styles pour le tableau agrandi */
.table-large {
  font-size: 1.1rem;
}

.table-large thead th {
  padding: 1.5rem 1rem;
  font-size: 1rem;
  font-weight: 700;
}

.table-large tbody td {
  padding: 1.5rem 1rem;
  font-size: 1rem;
}

.table-large h5 {
  font-size: 1.3rem;
  margin-bottom: 0.25rem;
}

/* Boutons d'action plus grands */
.btn-action {
  padding: 0.5rem 1rem;
  font-size: 0.9rem;
  font-weight: 500;
  border-radius: 0.5rem;
  min-width: 80px;
}

.btn-action:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* Amélioration de l'espacement */
.varient-table tbody tr {
  height: 80px;
}

/* Responsive pour les grands tableaux */
@media (max-width: 1200px) {
  .table-large {
    font-size: 1rem;
  }

  .table-large thead th,
  .table-large tbody td {
    padding: 1rem 0.75rem;
  }
}


