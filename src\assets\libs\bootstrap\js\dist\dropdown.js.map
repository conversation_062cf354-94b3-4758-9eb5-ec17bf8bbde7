{"version": 3, "file": "dropdown.js", "sources": ["../src/dropdown.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap dropdown.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport * as Popper from '@popperjs/core'\nimport BaseComponent from './base-component.js'\nimport EventHandler from './dom/event-handler.js'\nimport Manipulator from './dom/manipulator.js'\nimport SelectorEngine from './dom/selector-engine.js'\nimport {\n  defineJQueryPlugin,\n  execute,\n  getElement,\n  getNextActiveElement,\n  isDisabled,\n  isElement,\n  isRTL,\n  isVisible,\n  noop\n} from './util/index.js'\n\n/**\n * Constants\n */\n\nconst NAME = 'dropdown'\nconst DATA_KEY = 'bs.dropdown'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst ESCAPE_KEY = 'Escape'\nconst TAB_KEY = 'Tab'\nconst ARROW_UP_KEY = 'ArrowUp'\nconst ARROW_DOWN_KEY = 'ArrowDown'\nconst RIGHT_MOUSE_BUTTON = 2 // MouseEvent.button value for the secondary button, usually the right button\n\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_KEYDOWN_DATA_API = `keydown${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_KEYUP_DATA_API = `keyup${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_DROPUP = 'dropup'\nconst CLASS_NAME_DROPEND = 'dropend'\nconst CLASS_NAME_DROPSTART = 'dropstart'\nconst CLASS_NAME_DROPUP_CENTER = 'dropup-center'\nconst CLASS_NAME_DROPDOWN_CENTER = 'dropdown-center'\n\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"dropdown\"]:not(.disabled):not(:disabled)'\nconst SELECTOR_DATA_TOGGLE_SHOWN = `${SELECTOR_DATA_TOGGLE}.${CLASS_NAME_SHOW}`\nconst SELECTOR_MENU = '.dropdown-menu'\nconst SELECTOR_NAVBAR = '.navbar'\nconst SELECTOR_NAVBAR_NAV = '.navbar-nav'\nconst SELECTOR_VISIBLE_ITEMS = '.dropdown-menu .dropdown-item:not(.disabled):not(:disabled)'\n\nconst PLACEMENT_TOP = isRTL() ? 'top-end' : 'top-start'\nconst PLACEMENT_TOPEND = isRTL() ? 'top-start' : 'top-end'\nconst PLACEMENT_BOTTOM = isRTL() ? 'bottom-end' : 'bottom-start'\nconst PLACEMENT_BOTTOMEND = isRTL() ? 'bottom-start' : 'bottom-end'\nconst PLACEMENT_RIGHT = isRTL() ? 'left-start' : 'right-start'\nconst PLACEMENT_LEFT = isRTL() ? 'right-start' : 'left-start'\nconst PLACEMENT_TOPCENTER = 'top'\nconst PLACEMENT_BOTTOMCENTER = 'bottom'\n\nconst Default = {\n  autoClose: true,\n  boundary: 'clippingParents',\n  display: 'dynamic',\n  offset: [0, 2],\n  popperConfig: null,\n  reference: 'toggle'\n}\n\nconst DefaultType = {\n  autoClose: '(boolean|string)',\n  boundary: '(string|element)',\n  display: 'string',\n  offset: '(array|string|function)',\n  popperConfig: '(null|object|function)',\n  reference: '(string|element|object)'\n}\n\n/**\n * Class definition\n */\n\nclass Dropdown extends BaseComponent {\n  constructor(element, config) {\n    super(element, config)\n\n    this._popper = null\n    this._parent = this._element.parentNode // dropdown wrapper\n    // TODO: v6 revert #37011 & change markup https://getbootstrap.com/docs/5.3/forms/input-group/\n    this._menu = SelectorEngine.next(this._element, SELECTOR_MENU)[0] ||\n      SelectorEngine.prev(this._element, SELECTOR_MENU)[0] ||\n      SelectorEngine.findOne(SELECTOR_MENU, this._parent)\n    this._inNavbar = this._detectNavbar()\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  toggle() {\n    return this._isShown() ? this.hide() : this.show()\n  }\n\n  show() {\n    if (isDisabled(this._element) || this._isShown()) {\n      return\n    }\n\n    const relatedTarget = {\n      relatedTarget: this._element\n    }\n\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW, relatedTarget)\n\n    if (showEvent.defaultPrevented) {\n      return\n    }\n\n    this._createPopper()\n\n    // If this is a touch-enabled device we add extra\n    // empty mouseover listeners to the body's immediate children;\n    // only needed because of broken event delegation on iOS\n    // https://www.quirksmode.org/blog/archives/2014/02/mouse_event_bub.html\n    if ('ontouchstart' in document.documentElement && !this._parent.closest(SELECTOR_NAVBAR_NAV)) {\n      for (const element of [].concat(...document.body.children)) {\n        EventHandler.on(element, 'mouseover', noop)\n      }\n    }\n\n    this._element.focus()\n    this._element.setAttribute('aria-expanded', true)\n\n    this._menu.classList.add(CLASS_NAME_SHOW)\n    this._element.classList.add(CLASS_NAME_SHOW)\n    EventHandler.trigger(this._element, EVENT_SHOWN, relatedTarget)\n  }\n\n  hide() {\n    if (isDisabled(this._element) || !this._isShown()) {\n      return\n    }\n\n    const relatedTarget = {\n      relatedTarget: this._element\n    }\n\n    this._completeHide(relatedTarget)\n  }\n\n  dispose() {\n    if (this._popper) {\n      this._popper.destroy()\n    }\n\n    super.dispose()\n  }\n\n  update() {\n    this._inNavbar = this._detectNavbar()\n    if (this._popper) {\n      this._popper.update()\n    }\n  }\n\n  // Private\n  _completeHide(relatedTarget) {\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE, relatedTarget)\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    // If this is a touch-enabled device we remove the extra\n    // empty mouseover listeners we added for iOS support\n    if ('ontouchstart' in document.documentElement) {\n      for (const element of [].concat(...document.body.children)) {\n        EventHandler.off(element, 'mouseover', noop)\n      }\n    }\n\n    if (this._popper) {\n      this._popper.destroy()\n    }\n\n    this._menu.classList.remove(CLASS_NAME_SHOW)\n    this._element.classList.remove(CLASS_NAME_SHOW)\n    this._element.setAttribute('aria-expanded', 'false')\n    Manipulator.removeDataAttribute(this._menu, 'popper')\n    EventHandler.trigger(this._element, EVENT_HIDDEN, relatedTarget)\n  }\n\n  _getConfig(config) {\n    config = super._getConfig(config)\n\n    if (typeof config.reference === 'object' && !isElement(config.reference) &&\n      typeof config.reference.getBoundingClientRect !== 'function'\n    ) {\n      // Popper virtual elements require a getBoundingClientRect method\n      throw new TypeError(`${NAME.toUpperCase()}: Option \"reference\" provided type \"object\" without a required \"getBoundingClientRect\" method.`)\n    }\n\n    return config\n  }\n\n  _createPopper() {\n    if (typeof Popper === 'undefined') {\n      throw new TypeError('Bootstrap\\'s dropdowns require Popper (https://popper.js.org)')\n    }\n\n    let referenceElement = this._element\n\n    if (this._config.reference === 'parent') {\n      referenceElement = this._parent\n    } else if (isElement(this._config.reference)) {\n      referenceElement = getElement(this._config.reference)\n    } else if (typeof this._config.reference === 'object') {\n      referenceElement = this._config.reference\n    }\n\n    const popperConfig = this._getPopperConfig()\n    this._popper = Popper.createPopper(referenceElement, this._menu, popperConfig)\n  }\n\n  _isShown() {\n    return this._menu.classList.contains(CLASS_NAME_SHOW)\n  }\n\n  _getPlacement() {\n    const parentDropdown = this._parent\n\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPEND)) {\n      return PLACEMENT_RIGHT\n    }\n\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPSTART)) {\n      return PLACEMENT_LEFT\n    }\n\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPUP_CENTER)) {\n      return PLACEMENT_TOPCENTER\n    }\n\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPDOWN_CENTER)) {\n      return PLACEMENT_BOTTOMCENTER\n    }\n\n    // We need to trim the value because custom properties can also include spaces\n    const isEnd = getComputedStyle(this._menu).getPropertyValue('--bs-position').trim() === 'end'\n\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPUP)) {\n      return isEnd ? PLACEMENT_TOPEND : PLACEMENT_TOP\n    }\n\n    return isEnd ? PLACEMENT_BOTTOMEND : PLACEMENT_BOTTOM\n  }\n\n  _detectNavbar() {\n    return this._element.closest(SELECTOR_NAVBAR) !== null\n  }\n\n  _getOffset() {\n    const { offset } = this._config\n\n    if (typeof offset === 'string') {\n      return offset.split(',').map(value => Number.parseInt(value, 10))\n    }\n\n    if (typeof offset === 'function') {\n      return popperData => offset(popperData, this._element)\n    }\n\n    return offset\n  }\n\n  _getPopperConfig() {\n    const defaultBsPopperConfig = {\n      placement: this._getPlacement(),\n      modifiers: [{\n        name: 'preventOverflow',\n        options: {\n          boundary: this._config.boundary\n        }\n      },\n      {\n        name: 'offset',\n        options: {\n          offset: this._getOffset()\n        }\n      }]\n    }\n\n    // Disable Popper if we have a static display or Dropdown is in Navbar\n    if (this._inNavbar || this._config.display === 'static') {\n      Manipulator.setDataAttribute(this._menu, 'popper', 'static') // TODO: v6 remove\n      defaultBsPopperConfig.modifiers = [{\n        name: 'applyStyles',\n        enabled: false\n      }]\n    }\n\n    return {\n      ...defaultBsPopperConfig,\n      ...execute(this._config.popperConfig, [defaultBsPopperConfig])\n    }\n  }\n\n  _selectMenuItem({ key, target }) {\n    const items = SelectorEngine.find(SELECTOR_VISIBLE_ITEMS, this._menu).filter(element => isVisible(element))\n\n    if (!items.length) {\n      return\n    }\n\n    // if target isn't included in items (e.g. when expanding the dropdown)\n    // allow cycling to get the last item in case key equals ARROW_UP_KEY\n    getNextActiveElement(items, target, key === ARROW_DOWN_KEY, !items.includes(target)).focus()\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Dropdown.getOrCreateInstance(this, config)\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (typeof data[config] === 'undefined') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config]()\n    })\n  }\n\n  static clearMenus(event) {\n    if (event.button === RIGHT_MOUSE_BUTTON || (event.type === 'keyup' && event.key !== TAB_KEY)) {\n      return\n    }\n\n    const openToggles = SelectorEngine.find(SELECTOR_DATA_TOGGLE_SHOWN)\n\n    for (const toggle of openToggles) {\n      const context = Dropdown.getInstance(toggle)\n      if (!context || context._config.autoClose === false) {\n        continue\n      }\n\n      const composedPath = event.composedPath()\n      const isMenuTarget = composedPath.includes(context._menu)\n      if (\n        composedPath.includes(context._element) ||\n        (context._config.autoClose === 'inside' && !isMenuTarget) ||\n        (context._config.autoClose === 'outside' && isMenuTarget)\n      ) {\n        continue\n      }\n\n      // Tab navigation through the dropdown menu or events from contained inputs shouldn't close the menu\n      if (context._menu.contains(event.target) && ((event.type === 'keyup' && event.key === TAB_KEY) || /input|select|option|textarea|form/i.test(event.target.tagName))) {\n        continue\n      }\n\n      const relatedTarget = { relatedTarget: context._element }\n\n      if (event.type === 'click') {\n        relatedTarget.clickEvent = event\n      }\n\n      context._completeHide(relatedTarget)\n    }\n  }\n\n  static dataApiKeydownHandler(event) {\n    // If not an UP | DOWN | ESCAPE key => not a dropdown command\n    // If input/textarea && if key is other than ESCAPE => not a dropdown command\n\n    const isInput = /input|textarea/i.test(event.target.tagName)\n    const isEscapeEvent = event.key === ESCAPE_KEY\n    const isUpOrDownEvent = [ARROW_UP_KEY, ARROW_DOWN_KEY].includes(event.key)\n\n    if (!isUpOrDownEvent && !isEscapeEvent) {\n      return\n    }\n\n    if (isInput && !isEscapeEvent) {\n      return\n    }\n\n    event.preventDefault()\n\n    // TODO: v6 revert #37011 & change markup https://getbootstrap.com/docs/5.3/forms/input-group/\n    const getToggleButton = this.matches(SELECTOR_DATA_TOGGLE) ?\n      this :\n      (SelectorEngine.prev(this, SELECTOR_DATA_TOGGLE)[0] ||\n        SelectorEngine.next(this, SELECTOR_DATA_TOGGLE)[0] ||\n        SelectorEngine.findOne(SELECTOR_DATA_TOGGLE, event.delegateTarget.parentNode))\n\n    const instance = Dropdown.getOrCreateInstance(getToggleButton)\n\n    if (isUpOrDownEvent) {\n      event.stopPropagation()\n      instance.show()\n      instance._selectMenuItem(event)\n      return\n    }\n\n    if (instance._isShown()) { // else is escape and we check if it is shown\n      event.stopPropagation()\n      instance.hide()\n      getToggleButton.focus()\n    }\n  }\n}\n\n/**\n * Data API implementation\n */\n\nEventHandler.on(document, EVENT_KEYDOWN_DATA_API, SELECTOR_DATA_TOGGLE, Dropdown.dataApiKeydownHandler)\nEventHandler.on(document, EVENT_KEYDOWN_DATA_API, SELECTOR_MENU, Dropdown.dataApiKeydownHandler)\nEventHandler.on(document, EVENT_CLICK_DATA_API, Dropdown.clearMenus)\nEventHandler.on(document, EVENT_KEYUP_DATA_API, Dropdown.clearMenus)\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  event.preventDefault()\n  Dropdown.getOrCreateInstance(this).toggle()\n})\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Dropdown)\n\nexport default Dropdown\n"], "names": ["NAME", "DATA_KEY", "EVENT_KEY", "DATA_API_KEY", "ESCAPE_KEY", "TAB_KEY", "ARROW_UP_KEY", "ARROW_DOWN_KEY", "RIGHT_MOUSE_BUTTON", "EVENT_HIDE", "EVENT_HIDDEN", "EVENT_SHOW", "EVENT_SHOWN", "EVENT_CLICK_DATA_API", "EVENT_KEYDOWN_DATA_API", "EVENT_KEYUP_DATA_API", "CLASS_NAME_SHOW", "CLASS_NAME_DROPUP", "CLASS_NAME_DROPEND", "CLASS_NAME_DROPSTART", "CLASS_NAME_DROPUP_CENTER", "CLASS_NAME_DROPDOWN_CENTER", "SELECTOR_DATA_TOGGLE", "SELECTOR_DATA_TOGGLE_SHOWN", "SELECTOR_MENU", "SELECTOR_NAVBAR", "SELECTOR_NAVBAR_NAV", "SELECTOR_VISIBLE_ITEMS", "PLACEMENT_TOP", "isRTL", "PLACEMENT_TOPEND", "PLACEMENT_BOTTOM", "PLACEMENT_BOTTOMEND", "PLACEMENT_RIGHT", "PLACEMENT_LEFT", "PLACEMENT_TOPCENTER", "PLACEMENT_BOTTOMCENTER", "<PERSON><PERSON><PERSON>", "autoClose", "boundary", "display", "offset", "popperConfig", "reference", "DefaultType", "Dropdown", "BaseComponent", "constructor", "element", "config", "_popper", "_parent", "_element", "parentNode", "_menu", "SelectorEngine", "next", "prev", "findOne", "_inNavbar", "_detectNavbar", "toggle", "_isShown", "hide", "show", "isDisabled", "relatedTarget", "showEvent", "EventHandler", "trigger", "defaultPrevented", "_createPopper", "document", "documentElement", "closest", "concat", "body", "children", "on", "noop", "focus", "setAttribute", "classList", "add", "_completeHide", "dispose", "destroy", "update", "hideEvent", "off", "remove", "Manipulator", "removeDataAttribute", "_getConfig", "isElement", "getBoundingClientRect", "TypeError", "toUpperCase", "<PERSON><PERSON>", "referenceElement", "_config", "getElement", "_getPopperConfig", "createPopper", "contains", "_getPlacement", "parentDropdown", "isEnd", "getComputedStyle", "getPropertyValue", "trim", "_getOffset", "split", "map", "value", "Number", "parseInt", "popperData", "defaultBsPopperConfig", "placement", "modifiers", "name", "options", "setDataAttribute", "enabled", "execute", "_selectMenuItem", "key", "target", "items", "find", "filter", "isVisible", "length", "getNextActiveElement", "includes", "jQueryInterface", "each", "data", "getOrCreateInstance", "clearMenus", "event", "button", "type", "openToggles", "context", "getInstance", "<PERSON><PERSON><PERSON>", "isMenuTarget", "test", "tagName", "clickEvent", "dataApiKeydownHandler", "isInput", "isEscapeEvent", "isUpOrDownEvent", "preventDefault", "getToggleButton", "matches", "<PERSON><PERSON><PERSON><PERSON>", "instance", "stopPropagation", "defineJQueryPlugin"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAAA;EACA;EACA;EACA;EACA;EACA;;;EAmBA;EACA;EACA;;EAEA,MAAMA,IAAI,GAAG,UAAU,CAAA;EACvB,MAAMC,QAAQ,GAAG,aAAa,CAAA;EAC9B,MAAMC,SAAS,GAAI,CAAGD,CAAAA,EAAAA,QAAS,CAAC,CAAA,CAAA;EAChC,MAAME,YAAY,GAAG,WAAW,CAAA;EAEhC,MAAMC,UAAU,GAAG,QAAQ,CAAA;EAC3B,MAAMC,OAAO,GAAG,KAAK,CAAA;EACrB,MAAMC,YAAY,GAAG,SAAS,CAAA;EAC9B,MAAMC,cAAc,GAAG,WAAW,CAAA;EAClC,MAAMC,kBAAkB,GAAG,CAAC,CAAC;;EAE7B,MAAMC,UAAU,GAAI,CAAMP,IAAAA,EAAAA,SAAU,CAAC,CAAA,CAAA;EACrC,MAAMQ,YAAY,GAAI,CAAQR,MAAAA,EAAAA,SAAU,CAAC,CAAA,CAAA;EACzC,MAAMS,UAAU,GAAI,CAAMT,IAAAA,EAAAA,SAAU,CAAC,CAAA,CAAA;EACrC,MAAMU,WAAW,GAAI,CAAOV,KAAAA,EAAAA,SAAU,CAAC,CAAA,CAAA;EACvC,MAAMW,oBAAoB,GAAI,CAAA,KAAA,EAAOX,SAAU,CAAA,EAAEC,YAAa,CAAC,CAAA,CAAA;EAC/D,MAAMW,sBAAsB,GAAI,CAAA,OAAA,EAASZ,SAAU,CAAA,EAAEC,YAAa,CAAC,CAAA,CAAA;EACnE,MAAMY,oBAAoB,GAAI,CAAA,KAAA,EAAOb,SAAU,CAAA,EAAEC,YAAa,CAAC,CAAA,CAAA;EAE/D,MAAMa,eAAe,GAAG,MAAM,CAAA;EAC9B,MAAMC,iBAAiB,GAAG,QAAQ,CAAA;EAClC,MAAMC,kBAAkB,GAAG,SAAS,CAAA;EACpC,MAAMC,oBAAoB,GAAG,WAAW,CAAA;EACxC,MAAMC,wBAAwB,GAAG,eAAe,CAAA;EAChD,MAAMC,0BAA0B,GAAG,iBAAiB,CAAA;EAEpD,MAAMC,oBAAoB,GAAG,2DAA2D,CAAA;EACxF,MAAMC,0BAA0B,GAAI,CAAA,EAAED,oBAAqB,CAAA,CAAA,EAAGN,eAAgB,CAAC,CAAA,CAAA;EAC/E,MAAMQ,aAAa,GAAG,gBAAgB,CAAA;EACtC,MAAMC,eAAe,GAAG,SAAS,CAAA;EACjC,MAAMC,mBAAmB,GAAG,aAAa,CAAA;EACzC,MAAMC,sBAAsB,GAAG,6DAA6D,CAAA;EAE5F,MAAMC,aAAa,GAAGC,cAAK,EAAE,GAAG,SAAS,GAAG,WAAW,CAAA;EACvD,MAAMC,gBAAgB,GAAGD,cAAK,EAAE,GAAG,WAAW,GAAG,SAAS,CAAA;EAC1D,MAAME,gBAAgB,GAAGF,cAAK,EAAE,GAAG,YAAY,GAAG,cAAc,CAAA;EAChE,MAAMG,mBAAmB,GAAGH,cAAK,EAAE,GAAG,cAAc,GAAG,YAAY,CAAA;EACnE,MAAMI,eAAe,GAAGJ,cAAK,EAAE,GAAG,YAAY,GAAG,aAAa,CAAA;EAC9D,MAAMK,cAAc,GAAGL,cAAK,EAAE,GAAG,aAAa,GAAG,YAAY,CAAA;EAC7D,MAAMM,mBAAmB,GAAG,KAAK,CAAA;EACjC,MAAMC,sBAAsB,GAAG,QAAQ,CAAA;EAEvC,MAAMC,OAAO,GAAG;EACdC,EAAAA,SAAS,EAAE,IAAI;EACfC,EAAAA,QAAQ,EAAE,iBAAiB;EAC3BC,EAAAA,OAAO,EAAE,SAAS;EAClBC,EAAAA,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;EACdC,EAAAA,YAAY,EAAE,IAAI;EAClBC,EAAAA,SAAS,EAAE,QAAA;EACb,CAAC,CAAA;EAED,MAAMC,WAAW,GAAG;EAClBN,EAAAA,SAAS,EAAE,kBAAkB;EAC7BC,EAAAA,QAAQ,EAAE,kBAAkB;EAC5BC,EAAAA,OAAO,EAAE,QAAQ;EACjBC,EAAAA,MAAM,EAAE,yBAAyB;EACjCC,EAAAA,YAAY,EAAE,wBAAwB;EACtCC,EAAAA,SAAS,EAAE,yBAAA;EACb,CAAC,CAAA;;EAED;EACA;EACA;;EAEA,MAAME,QAAQ,SAASC,aAAa,CAAC;EACnCC,EAAAA,WAAWA,CAACC,OAAO,EAAEC,MAAM,EAAE;EAC3B,IAAA,KAAK,CAACD,OAAO,EAAEC,MAAM,CAAC,CAAA;MAEtB,IAAI,CAACC,OAAO,GAAG,IAAI,CAAA;MACnB,IAAI,CAACC,OAAO,GAAG,IAAI,CAACC,QAAQ,CAACC,UAAU,CAAC;EACxC;EACA,IAAA,IAAI,CAACC,KAAK,GAAGC,cAAc,CAACC,IAAI,CAAC,IAAI,CAACJ,QAAQ,EAAE5B,aAAa,CAAC,CAAC,CAAC,CAAC,IAC/D+B,cAAc,CAACE,IAAI,CAAC,IAAI,CAACL,QAAQ,EAAE5B,aAAa,CAAC,CAAC,CAAC,CAAC,IACpD+B,cAAc,CAACG,OAAO,CAAClC,aAAa,EAAE,IAAI,CAAC2B,OAAO,CAAC,CAAA;EACrD,IAAA,IAAI,CAACQ,SAAS,GAAG,IAAI,CAACC,aAAa,EAAE,CAAA;EACvC,GAAA;;EAEA;IACA,WAAWvB,OAAOA,GAAG;EACnB,IAAA,OAAOA,OAAO,CAAA;EAChB,GAAA;IAEA,WAAWO,WAAWA,GAAG;EACvB,IAAA,OAAOA,WAAW,CAAA;EACpB,GAAA;IAEA,WAAW5C,IAAIA,GAAG;EAChB,IAAA,OAAOA,IAAI,CAAA;EACb,GAAA;;EAEA;EACA6D,EAAAA,MAAMA,GAAG;EACP,IAAA,OAAO,IAAI,CAACC,QAAQ,EAAE,GAAG,IAAI,CAACC,IAAI,EAAE,GAAG,IAAI,CAACC,IAAI,EAAE,CAAA;EACpD,GAAA;EAEAA,EAAAA,IAAIA,GAAG;EACL,IAAA,IAAIC,mBAAU,CAAC,IAAI,CAACb,QAAQ,CAAC,IAAI,IAAI,CAACU,QAAQ,EAAE,EAAE;EAChD,MAAA,OAAA;EACF,KAAA;EAEA,IAAA,MAAMI,aAAa,GAAG;QACpBA,aAAa,EAAE,IAAI,CAACd,QAAAA;OACrB,CAAA;EAED,IAAA,MAAMe,SAAS,GAAGC,YAAY,CAACC,OAAO,CAAC,IAAI,CAACjB,QAAQ,EAAEzC,UAAU,EAAEuD,aAAa,CAAC,CAAA;MAEhF,IAAIC,SAAS,CAACG,gBAAgB,EAAE;EAC9B,MAAA,OAAA;EACF,KAAA;MAEA,IAAI,CAACC,aAAa,EAAE,CAAA;;EAEpB;EACA;EACA;EACA;EACA,IAAA,IAAI,cAAc,IAAIC,QAAQ,CAACC,eAAe,IAAI,CAAC,IAAI,CAACtB,OAAO,CAACuB,OAAO,CAAChD,mBAAmB,CAAC,EAAE;EAC5F,MAAA,KAAK,MAAMsB,OAAO,IAAI,EAAE,CAAC2B,MAAM,CAAC,GAAGH,QAAQ,CAACI,IAAI,CAACC,QAAQ,CAAC,EAAE;UAC1DT,YAAY,CAACU,EAAE,CAAC9B,OAAO,EAAE,WAAW,EAAE+B,aAAI,CAAC,CAAA;EAC7C,OAAA;EACF,KAAA;EAEA,IAAA,IAAI,CAAC3B,QAAQ,CAAC4B,KAAK,EAAE,CAAA;MACrB,IAAI,CAAC5B,QAAQ,CAAC6B,YAAY,CAAC,eAAe,EAAE,IAAI,CAAC,CAAA;MAEjD,IAAI,CAAC3B,KAAK,CAAC4B,SAAS,CAACC,GAAG,CAACnE,eAAe,CAAC,CAAA;MACzC,IAAI,CAACoC,QAAQ,CAAC8B,SAAS,CAACC,GAAG,CAACnE,eAAe,CAAC,CAAA;MAC5CoD,YAAY,CAACC,OAAO,CAAC,IAAI,CAACjB,QAAQ,EAAExC,WAAW,EAAEsD,aAAa,CAAC,CAAA;EACjE,GAAA;EAEAH,EAAAA,IAAIA,GAAG;EACL,IAAA,IAAIE,mBAAU,CAAC,IAAI,CAACb,QAAQ,CAAC,IAAI,CAAC,IAAI,CAACU,QAAQ,EAAE,EAAE;EACjD,MAAA,OAAA;EACF,KAAA;EAEA,IAAA,MAAMI,aAAa,GAAG;QACpBA,aAAa,EAAE,IAAI,CAACd,QAAAA;OACrB,CAAA;EAED,IAAA,IAAI,CAACgC,aAAa,CAAClB,aAAa,CAAC,CAAA;EACnC,GAAA;EAEAmB,EAAAA,OAAOA,GAAG;MACR,IAAI,IAAI,CAACnC,OAAO,EAAE;EAChB,MAAA,IAAI,CAACA,OAAO,CAACoC,OAAO,EAAE,CAAA;EACxB,KAAA;MAEA,KAAK,CAACD,OAAO,EAAE,CAAA;EACjB,GAAA;EAEAE,EAAAA,MAAMA,GAAG;EACP,IAAA,IAAI,CAAC5B,SAAS,GAAG,IAAI,CAACC,aAAa,EAAE,CAAA;MACrC,IAAI,IAAI,CAACV,OAAO,EAAE;EAChB,MAAA,IAAI,CAACA,OAAO,CAACqC,MAAM,EAAE,CAAA;EACvB,KAAA;EACF,GAAA;;EAEA;IACAH,aAAaA,CAAClB,aAAa,EAAE;EAC3B,IAAA,MAAMsB,SAAS,GAAGpB,YAAY,CAACC,OAAO,CAAC,IAAI,CAACjB,QAAQ,EAAE3C,UAAU,EAAEyD,aAAa,CAAC,CAAA;MAChF,IAAIsB,SAAS,CAAClB,gBAAgB,EAAE;EAC9B,MAAA,OAAA;EACF,KAAA;;EAEA;EACA;EACA,IAAA,IAAI,cAAc,IAAIE,QAAQ,CAACC,eAAe,EAAE;EAC9C,MAAA,KAAK,MAAMzB,OAAO,IAAI,EAAE,CAAC2B,MAAM,CAAC,GAAGH,QAAQ,CAACI,IAAI,CAACC,QAAQ,CAAC,EAAE;UAC1DT,YAAY,CAACqB,GAAG,CAACzC,OAAO,EAAE,WAAW,EAAE+B,aAAI,CAAC,CAAA;EAC9C,OAAA;EACF,KAAA;MAEA,IAAI,IAAI,CAAC7B,OAAO,EAAE;EAChB,MAAA,IAAI,CAACA,OAAO,CAACoC,OAAO,EAAE,CAAA;EACxB,KAAA;MAEA,IAAI,CAAChC,KAAK,CAAC4B,SAAS,CAACQ,MAAM,CAAC1E,eAAe,CAAC,CAAA;MAC5C,IAAI,CAACoC,QAAQ,CAAC8B,SAAS,CAACQ,MAAM,CAAC1E,eAAe,CAAC,CAAA;MAC/C,IAAI,CAACoC,QAAQ,CAAC6B,YAAY,CAAC,eAAe,EAAE,OAAO,CAAC,CAAA;MACpDU,WAAW,CAACC,mBAAmB,CAAC,IAAI,CAACtC,KAAK,EAAE,QAAQ,CAAC,CAAA;MACrDc,YAAY,CAACC,OAAO,CAAC,IAAI,CAACjB,QAAQ,EAAE1C,YAAY,EAAEwD,aAAa,CAAC,CAAA;EAClE,GAAA;IAEA2B,UAAUA,CAAC5C,MAAM,EAAE;EACjBA,IAAAA,MAAM,GAAG,KAAK,CAAC4C,UAAU,CAAC5C,MAAM,CAAC,CAAA;MAEjC,IAAI,OAAOA,MAAM,CAACN,SAAS,KAAK,QAAQ,IAAI,CAACmD,kBAAS,CAAC7C,MAAM,CAACN,SAAS,CAAC,IACtE,OAAOM,MAAM,CAACN,SAAS,CAACoD,qBAAqB,KAAK,UAAU,EAC5D;EACA;QACA,MAAM,IAAIC,SAAS,CAAE,CAAEhG,EAAAA,IAAI,CAACiG,WAAW,EAAG,CAAA,8FAAA,CAA+F,CAAC,CAAA;EAC5I,KAAA;EAEA,IAAA,OAAOhD,MAAM,CAAA;EACf,GAAA;EAEAsB,EAAAA,aAAaA,GAAG;EACd,IAAA,IAAI,OAAO2B,iBAAM,KAAK,WAAW,EAAE;EACjC,MAAA,MAAM,IAAIF,SAAS,CAAC,+DAA+D,CAAC,CAAA;EACtF,KAAA;EAEA,IAAA,IAAIG,gBAAgB,GAAG,IAAI,CAAC/C,QAAQ,CAAA;EAEpC,IAAA,IAAI,IAAI,CAACgD,OAAO,CAACzD,SAAS,KAAK,QAAQ,EAAE;QACvCwD,gBAAgB,GAAG,IAAI,CAAChD,OAAO,CAAA;OAChC,MAAM,IAAI2C,kBAAS,CAAC,IAAI,CAACM,OAAO,CAACzD,SAAS,CAAC,EAAE;QAC5CwD,gBAAgB,GAAGE,mBAAU,CAAC,IAAI,CAACD,OAAO,CAACzD,SAAS,CAAC,CAAA;OACtD,MAAM,IAAI,OAAO,IAAI,CAACyD,OAAO,CAACzD,SAAS,KAAK,QAAQ,EAAE;EACrDwD,MAAAA,gBAAgB,GAAG,IAAI,CAACC,OAAO,CAACzD,SAAS,CAAA;EAC3C,KAAA;EAEA,IAAA,MAAMD,YAAY,GAAG,IAAI,CAAC4D,gBAAgB,EAAE,CAAA;EAC5C,IAAA,IAAI,CAACpD,OAAO,GAAGgD,iBAAM,CAACK,YAAY,CAACJ,gBAAgB,EAAE,IAAI,CAAC7C,KAAK,EAAEZ,YAAY,CAAC,CAAA;EAChF,GAAA;EAEAoB,EAAAA,QAAQA,GAAG;MACT,OAAO,IAAI,CAACR,KAAK,CAAC4B,SAAS,CAACsB,QAAQ,CAACxF,eAAe,CAAC,CAAA;EACvD,GAAA;EAEAyF,EAAAA,aAAaA,GAAG;EACd,IAAA,MAAMC,cAAc,GAAG,IAAI,CAACvD,OAAO,CAAA;MAEnC,IAAIuD,cAAc,CAACxB,SAAS,CAACsB,QAAQ,CAACtF,kBAAkB,CAAC,EAAE;EACzD,MAAA,OAAOe,eAAe,CAAA;EACxB,KAAA;MAEA,IAAIyE,cAAc,CAACxB,SAAS,CAACsB,QAAQ,CAACrF,oBAAoB,CAAC,EAAE;EAC3D,MAAA,OAAOe,cAAc,CAAA;EACvB,KAAA;MAEA,IAAIwE,cAAc,CAACxB,SAAS,CAACsB,QAAQ,CAACpF,wBAAwB,CAAC,EAAE;EAC/D,MAAA,OAAOe,mBAAmB,CAAA;EAC5B,KAAA;MAEA,IAAIuE,cAAc,CAACxB,SAAS,CAACsB,QAAQ,CAACnF,0BAA0B,CAAC,EAAE;EACjE,MAAA,OAAOe,sBAAsB,CAAA;EAC/B,KAAA;;EAEA;EACA,IAAA,MAAMuE,KAAK,GAAGC,gBAAgB,CAAC,IAAI,CAACtD,KAAK,CAAC,CAACuD,gBAAgB,CAAC,eAAe,CAAC,CAACC,IAAI,EAAE,KAAK,KAAK,CAAA;MAE7F,IAAIJ,cAAc,CAACxB,SAAS,CAACsB,QAAQ,CAACvF,iBAAiB,CAAC,EAAE;EACxD,MAAA,OAAO0F,KAAK,GAAG7E,gBAAgB,GAAGF,aAAa,CAAA;EACjD,KAAA;EAEA,IAAA,OAAO+E,KAAK,GAAG3E,mBAAmB,GAAGD,gBAAgB,CAAA;EACvD,GAAA;EAEA6B,EAAAA,aAAaA,GAAG;MACd,OAAO,IAAI,CAACR,QAAQ,CAACsB,OAAO,CAACjD,eAAe,CAAC,KAAK,IAAI,CAAA;EACxD,GAAA;EAEAsF,EAAAA,UAAUA,GAAG;MACX,MAAM;EAAEtE,MAAAA,MAAAA;OAAQ,GAAG,IAAI,CAAC2D,OAAO,CAAA;EAE/B,IAAA,IAAI,OAAO3D,MAAM,KAAK,QAAQ,EAAE;EAC9B,MAAA,OAAOA,MAAM,CAACuE,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAACC,KAAK,IAAIC,MAAM,CAACC,QAAQ,CAACF,KAAK,EAAE,EAAE,CAAC,CAAC,CAAA;EACnE,KAAA;EAEA,IAAA,IAAI,OAAOzE,MAAM,KAAK,UAAU,EAAE;QAChC,OAAO4E,UAAU,IAAI5E,MAAM,CAAC4E,UAAU,EAAE,IAAI,CAACjE,QAAQ,CAAC,CAAA;EACxD,KAAA;EAEA,IAAA,OAAOX,MAAM,CAAA;EACf,GAAA;EAEA6D,EAAAA,gBAAgBA,GAAG;EACjB,IAAA,MAAMgB,qBAAqB,GAAG;EAC5BC,MAAAA,SAAS,EAAE,IAAI,CAACd,aAAa,EAAE;EAC/Be,MAAAA,SAAS,EAAE,CAAC;EACVC,QAAAA,IAAI,EAAE,iBAAiB;EACvBC,QAAAA,OAAO,EAAE;EACPnF,UAAAA,QAAQ,EAAE,IAAI,CAAC6D,OAAO,CAAC7D,QAAAA;EACzB,SAAA;EACF,OAAC,EACD;EACEkF,QAAAA,IAAI,EAAE,QAAQ;EACdC,QAAAA,OAAO,EAAE;EACPjF,UAAAA,MAAM,EAAE,IAAI,CAACsE,UAAU,EAAC;EAC1B,SAAA;SACD,CAAA;OACF,CAAA;;EAED;MACA,IAAI,IAAI,CAACpD,SAAS,IAAI,IAAI,CAACyC,OAAO,CAAC5D,OAAO,KAAK,QAAQ,EAAE;QACvDmD,WAAW,CAACgC,gBAAgB,CAAC,IAAI,CAACrE,KAAK,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;QAC7DgE,qBAAqB,CAACE,SAAS,GAAG,CAAC;EACjCC,QAAAA,IAAI,EAAE,aAAa;EACnBG,QAAAA,OAAO,EAAE,KAAA;EACX,OAAC,CAAC,CAAA;EACJ,KAAA;MAEA,OAAO;EACL,MAAA,GAAGN,qBAAqB;QACxB,GAAGO,gBAAO,CAAC,IAAI,CAACzB,OAAO,CAAC1D,YAAY,EAAE,CAAC4E,qBAAqB,CAAC,CAAA;OAC9D,CAAA;EACH,GAAA;EAEAQ,EAAAA,eAAeA,CAAC;MAAEC,GAAG;EAAEC,IAAAA,MAAAA;EAAO,GAAC,EAAE;MAC/B,MAAMC,KAAK,GAAG1E,cAAc,CAAC2E,IAAI,CAACvG,sBAAsB,EAAE,IAAI,CAAC2B,KAAK,CAAC,CAAC6E,MAAM,CAACnF,OAAO,IAAIoF,kBAAS,CAACpF,OAAO,CAAC,CAAC,CAAA;EAE3G,IAAA,IAAI,CAACiF,KAAK,CAACI,MAAM,EAAE;EACjB,MAAA,OAAA;EACF,KAAA;;EAEA;EACA;MACAC,6BAAoB,CAACL,KAAK,EAAED,MAAM,EAAED,GAAG,KAAKxH,cAAc,EAAE,CAAC0H,KAAK,CAACM,QAAQ,CAACP,MAAM,CAAC,CAAC,CAAChD,KAAK,EAAE,CAAA;EAC9F,GAAA;;EAEA;IACA,OAAOwD,eAAeA,CAACvF,MAAM,EAAE;EAC7B,IAAA,OAAO,IAAI,CAACwF,IAAI,CAAC,YAAY;QAC3B,MAAMC,IAAI,GAAG7F,QAAQ,CAAC8F,mBAAmB,CAAC,IAAI,EAAE1F,MAAM,CAAC,CAAA;EAEvD,MAAA,IAAI,OAAOA,MAAM,KAAK,QAAQ,EAAE;EAC9B,QAAA,OAAA;EACF,OAAA;EAEA,MAAA,IAAI,OAAOyF,IAAI,CAACzF,MAAM,CAAC,KAAK,WAAW,EAAE;EACvC,QAAA,MAAM,IAAI+C,SAAS,CAAE,CAAmB/C,iBAAAA,EAAAA,MAAO,GAAE,CAAC,CAAA;EACpD,OAAA;EAEAyF,MAAAA,IAAI,CAACzF,MAAM,CAAC,EAAE,CAAA;EAChB,KAAC,CAAC,CAAA;EACJ,GAAA;IAEA,OAAO2F,UAAUA,CAACC,KAAK,EAAE;EACvB,IAAA,IAAIA,KAAK,CAACC,MAAM,KAAKtI,kBAAkB,IAAKqI,KAAK,CAACE,IAAI,KAAK,OAAO,IAAIF,KAAK,CAACd,GAAG,KAAK1H,OAAQ,EAAE;EAC5F,MAAA,OAAA;EACF,KAAA;EAEA,IAAA,MAAM2I,WAAW,GAAGzF,cAAc,CAAC2E,IAAI,CAAC3G,0BAA0B,CAAC,CAAA;EAEnE,IAAA,KAAK,MAAMsC,MAAM,IAAImF,WAAW,EAAE;EAChC,MAAA,MAAMC,OAAO,GAAGpG,QAAQ,CAACqG,WAAW,CAACrF,MAAM,CAAC,CAAA;QAC5C,IAAI,CAACoF,OAAO,IAAIA,OAAO,CAAC7C,OAAO,CAAC9D,SAAS,KAAK,KAAK,EAAE;EACnD,QAAA,SAAA;EACF,OAAA;EAEA,MAAA,MAAM6G,YAAY,GAAGN,KAAK,CAACM,YAAY,EAAE,CAAA;QACzC,MAAMC,YAAY,GAAGD,YAAY,CAACZ,QAAQ,CAACU,OAAO,CAAC3F,KAAK,CAAC,CAAA;EACzD,MAAA,IACE6F,YAAY,CAACZ,QAAQ,CAACU,OAAO,CAAC7F,QAAQ,CAAC,IACtC6F,OAAO,CAAC7C,OAAO,CAAC9D,SAAS,KAAK,QAAQ,IAAI,CAAC8G,YAAa,IACxDH,OAAO,CAAC7C,OAAO,CAAC9D,SAAS,KAAK,SAAS,IAAI8G,YAAa,EACzD;EACA,QAAA,SAAA;EACF,OAAA;;EAEA;EACA,MAAA,IAAIH,OAAO,CAAC3F,KAAK,CAACkD,QAAQ,CAACqC,KAAK,CAACb,MAAM,CAAC,KAAMa,KAAK,CAACE,IAAI,KAAK,OAAO,IAAIF,KAAK,CAACd,GAAG,KAAK1H,OAAO,IAAK,oCAAoC,CAACgJ,IAAI,CAACR,KAAK,CAACb,MAAM,CAACsB,OAAO,CAAC,CAAC,EAAE;EAClK,QAAA,SAAA;EACF,OAAA;EAEA,MAAA,MAAMpF,aAAa,GAAG;UAAEA,aAAa,EAAE+E,OAAO,CAAC7F,QAAAA;SAAU,CAAA;EAEzD,MAAA,IAAIyF,KAAK,CAACE,IAAI,KAAK,OAAO,EAAE;UAC1B7E,aAAa,CAACqF,UAAU,GAAGV,KAAK,CAAA;EAClC,OAAA;EAEAI,MAAAA,OAAO,CAAC7D,aAAa,CAAClB,aAAa,CAAC,CAAA;EACtC,KAAA;EACF,GAAA;IAEA,OAAOsF,qBAAqBA,CAACX,KAAK,EAAE;EAClC;EACA;;MAEA,MAAMY,OAAO,GAAG,iBAAiB,CAACJ,IAAI,CAACR,KAAK,CAACb,MAAM,CAACsB,OAAO,CAAC,CAAA;EAC5D,IAAA,MAAMI,aAAa,GAAGb,KAAK,CAACd,GAAG,KAAK3H,UAAU,CAAA;EAC9C,IAAA,MAAMuJ,eAAe,GAAG,CAACrJ,YAAY,EAAEC,cAAc,CAAC,CAACgI,QAAQ,CAACM,KAAK,CAACd,GAAG,CAAC,CAAA;EAE1E,IAAA,IAAI,CAAC4B,eAAe,IAAI,CAACD,aAAa,EAAE;EACtC,MAAA,OAAA;EACF,KAAA;EAEA,IAAA,IAAID,OAAO,IAAI,CAACC,aAAa,EAAE;EAC7B,MAAA,OAAA;EACF,KAAA;MAEAb,KAAK,CAACe,cAAc,EAAE,CAAA;;EAEtB;MACA,MAAMC,eAAe,GAAG,IAAI,CAACC,OAAO,CAACxI,oBAAoB,CAAC,GACxD,IAAI,GACHiC,cAAc,CAACE,IAAI,CAAC,IAAI,EAAEnC,oBAAoB,CAAC,CAAC,CAAC,CAAC,IACjDiC,cAAc,CAACC,IAAI,CAAC,IAAI,EAAElC,oBAAoB,CAAC,CAAC,CAAC,CAAC,IAClDiC,cAAc,CAACG,OAAO,CAACpC,oBAAoB,EAAEuH,KAAK,CAACkB,cAAc,CAAC1G,UAAU,CAAE,CAAA;EAElF,IAAA,MAAM2G,QAAQ,GAAGnH,QAAQ,CAAC8F,mBAAmB,CAACkB,eAAe,CAAC,CAAA;EAE9D,IAAA,IAAIF,eAAe,EAAE;QACnBd,KAAK,CAACoB,eAAe,EAAE,CAAA;QACvBD,QAAQ,CAAChG,IAAI,EAAE,CAAA;EACfgG,MAAAA,QAAQ,CAAClC,eAAe,CAACe,KAAK,CAAC,CAAA;EAC/B,MAAA,OAAA;EACF,KAAA;EAEA,IAAA,IAAImB,QAAQ,CAAClG,QAAQ,EAAE,EAAE;EAAE;QACzB+E,KAAK,CAACoB,eAAe,EAAE,CAAA;QACvBD,QAAQ,CAACjG,IAAI,EAAE,CAAA;QACf8F,eAAe,CAAC7E,KAAK,EAAE,CAAA;EACzB,KAAA;EACF,GAAA;EACF,CAAA;;EAEA;EACA;EACA;;EAEAZ,YAAY,CAACU,EAAE,CAACN,QAAQ,EAAE1D,sBAAsB,EAAEQ,oBAAoB,EAAEuB,QAAQ,CAAC2G,qBAAqB,CAAC,CAAA;EACvGpF,YAAY,CAACU,EAAE,CAACN,QAAQ,EAAE1D,sBAAsB,EAAEU,aAAa,EAAEqB,QAAQ,CAAC2G,qBAAqB,CAAC,CAAA;EAChGpF,YAAY,CAACU,EAAE,CAACN,QAAQ,EAAE3D,oBAAoB,EAAEgC,QAAQ,CAAC+F,UAAU,CAAC,CAAA;EACpExE,YAAY,CAACU,EAAE,CAACN,QAAQ,EAAEzD,oBAAoB,EAAE8B,QAAQ,CAAC+F,UAAU,CAAC,CAAA;EACpExE,YAAY,CAACU,EAAE,CAACN,QAAQ,EAAE3D,oBAAoB,EAAES,oBAAoB,EAAE,UAAUuH,KAAK,EAAE;IACrFA,KAAK,CAACe,cAAc,EAAE,CAAA;IACtB/G,QAAQ,CAAC8F,mBAAmB,CAAC,IAAI,CAAC,CAAC9E,MAAM,EAAE,CAAA;EAC7C,CAAC,CAAC,CAAA;;EAEF;EACA;EACA;;AAEAqG,6BAAkB,CAACrH,QAAQ,CAAC;;;;;;;;"}