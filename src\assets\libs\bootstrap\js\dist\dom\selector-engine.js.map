{"version": 3, "file": "selector-engine.js", "sources": ["../../src/dom/selector-engine.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap dom/selector-engine.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { isDisabled, isVisible, parseSelector } from '../util/index.js'\n\nconst getSelector = element => {\n  let selector = element.getAttribute('data-bs-target')\n\n  if (!selector || selector === '#') {\n    let hrefAttribute = element.getAttribute('href')\n\n    // The only valid content that could double as a selector are IDs or classes,\n    // so everything starting with `#` or `.`. If a \"real\" URL is used as the selector,\n    // `document.querySelector` will rightfully complain it is invalid.\n    // See https://github.com/twbs/bootstrap/issues/32273\n    if (!hrefAttribute || (!hrefAttribute.includes('#') && !hrefAttribute.startsWith('.'))) {\n      return null\n    }\n\n    // Just in case some CMS puts out a full URL with the anchor appended\n    if (hrefAttribute.includes('#') && !hrefAttribute.startsWith('#')) {\n      hrefAttribute = `#${hrefAttribute.split('#')[1]}`\n    }\n\n    selector = hrefAttribute && hrefAttribute !== '#' ? hrefAttribute.trim() : null\n  }\n\n  return selector ? selector.split(',').map(sel => parseSelector(sel)).join(',') : null\n}\n\nconst SelectorEngine = {\n  find(selector, element = document.documentElement) {\n    return [].concat(...Element.prototype.querySelectorAll.call(element, selector))\n  },\n\n  findOne(selector, element = document.documentElement) {\n    return Element.prototype.querySelector.call(element, selector)\n  },\n\n  children(element, selector) {\n    return [].concat(...element.children).filter(child => child.matches(selector))\n  },\n\n  parents(element, selector) {\n    const parents = []\n    let ancestor = element.parentNode.closest(selector)\n\n    while (ancestor) {\n      parents.push(ancestor)\n      ancestor = ancestor.parentNode.closest(selector)\n    }\n\n    return parents\n  },\n\n  prev(element, selector) {\n    let previous = element.previousElementSibling\n\n    while (previous) {\n      if (previous.matches(selector)) {\n        return [previous]\n      }\n\n      previous = previous.previousElementSibling\n    }\n\n    return []\n  },\n  // TODO: this is now unused; remove later along with prev()\n  next(element, selector) {\n    let next = element.nextElementSibling\n\n    while (next) {\n      if (next.matches(selector)) {\n        return [next]\n      }\n\n      next = next.nextElementSibling\n    }\n\n    return []\n  },\n\n  focusableChildren(element) {\n    const focusables = [\n      'a',\n      'button',\n      'input',\n      'textarea',\n      'select',\n      'details',\n      '[tabindex]',\n      '[contenteditable=\"true\"]'\n    ].map(selector => `${selector}:not([tabindex^=\"-\"])`).join(',')\n\n    return this.find(focusables, element).filter(el => !isDisabled(el) && isVisible(el))\n  },\n\n  getSelectorFromElement(element) {\n    const selector = getSelector(element)\n\n    if (selector) {\n      return SelectorEngine.findOne(selector) ? selector : null\n    }\n\n    return null\n  },\n\n  getElementFromSelector(element) {\n    const selector = getSelector(element)\n\n    return selector ? SelectorEngine.findOne(selector) : null\n  },\n\n  getMultipleElementsFromSelector(element) {\n    const selector = getSelector(element)\n\n    return selector ? SelectorEngine.find(selector) : []\n  }\n}\n\nexport default SelectorEngine\n"], "names": ["getSelector", "element", "selector", "getAttribute", "hrefAttribute", "includes", "startsWith", "split", "trim", "map", "sel", "parseSelector", "join", "SelectorEngine", "find", "document", "documentElement", "concat", "Element", "prototype", "querySelectorAll", "call", "findOne", "querySelector", "children", "filter", "child", "matches", "parents", "ancestor", "parentNode", "closest", "push", "prev", "previous", "previousElementSibling", "next", "nextElement<PERSON><PERSON>ling", "focusableC<PERSON><PERSON>n", "focusables", "el", "isDisabled", "isVisible", "getSelectorFromElement", "getElementFromSelector", "getMultipleElementsFromSelector"], "mappings": ";;;;;;;;;;;EAAA;EACA;EACA;EACA;EACA;EACA;;EAIA,MAAMA,WAAW,GAAGC,OAAO,IAAI;EAC7B,EAAA,IAAIC,QAAQ,GAAGD,OAAO,CAACE,YAAY,CAAC,gBAAgB,CAAC,CAAA;EAErD,EAAA,IAAI,CAACD,QAAQ,IAAIA,QAAQ,KAAK,GAAG,EAAE;EACjC,IAAA,IAAIE,aAAa,GAAGH,OAAO,CAACE,YAAY,CAAC,MAAM,CAAC,CAAA;;EAEhD;EACA;EACA;EACA;EACA,IAAA,IAAI,CAACC,aAAa,IAAK,CAACA,aAAa,CAACC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAACD,aAAa,CAACE,UAAU,CAAC,GAAG,CAAE,EAAE;EACtF,MAAA,OAAO,IAAI,CAAA;EACb,KAAA;;EAEA;EACA,IAAA,IAAIF,aAAa,CAACC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAACD,aAAa,CAACE,UAAU,CAAC,GAAG,CAAC,EAAE;QACjEF,aAAa,GAAI,CAAGA,CAAAA,EAAAA,aAAa,CAACG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAE,CAAC,CAAA,CAAA;EACnD,KAAA;EAEAL,IAAAA,QAAQ,GAAGE,aAAa,IAAIA,aAAa,KAAK,GAAG,GAAGA,aAAa,CAACI,IAAI,EAAE,GAAG,IAAI,CAAA;EACjF,GAAA;IAEA,OAAON,QAAQ,GAAGA,QAAQ,CAACK,KAAK,CAAC,GAAG,CAAC,CAACE,GAAG,CAACC,GAAG,IAAIC,sBAAa,CAACD,GAAG,CAAC,CAAC,CAACE,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,CAAA;EACvF,CAAC,CAAA;AAED,QAAMC,cAAc,GAAG;IACrBC,IAAIA,CAACZ,QAAQ,EAAED,OAAO,GAAGc,QAAQ,CAACC,eAAe,EAAE;EACjD,IAAA,OAAO,EAAE,CAACC,MAAM,CAAC,GAAGC,OAAO,CAACC,SAAS,CAACC,gBAAgB,CAACC,IAAI,CAACpB,OAAO,EAAEC,QAAQ,CAAC,CAAC,CAAA;KAChF;IAEDoB,OAAOA,CAACpB,QAAQ,EAAED,OAAO,GAAGc,QAAQ,CAACC,eAAe,EAAE;MACpD,OAAOE,OAAO,CAACC,SAAS,CAACI,aAAa,CAACF,IAAI,CAACpB,OAAO,EAAEC,QAAQ,CAAC,CAAA;KAC/D;EAEDsB,EAAAA,QAAQA,CAACvB,OAAO,EAAEC,QAAQ,EAAE;MAC1B,OAAO,EAAE,CAACe,MAAM,CAAC,GAAGhB,OAAO,CAACuB,QAAQ,CAAC,CAACC,MAAM,CAACC,KAAK,IAAIA,KAAK,CAACC,OAAO,CAACzB,QAAQ,CAAC,CAAC,CAAA;KAC/E;EAED0B,EAAAA,OAAOA,CAAC3B,OAAO,EAAEC,QAAQ,EAAE;MACzB,MAAM0B,OAAO,GAAG,EAAE,CAAA;MAClB,IAAIC,QAAQ,GAAG5B,OAAO,CAAC6B,UAAU,CAACC,OAAO,CAAC7B,QAAQ,CAAC,CAAA;EAEnD,IAAA,OAAO2B,QAAQ,EAAE;EACfD,MAAAA,OAAO,CAACI,IAAI,CAACH,QAAQ,CAAC,CAAA;QACtBA,QAAQ,GAAGA,QAAQ,CAACC,UAAU,CAACC,OAAO,CAAC7B,QAAQ,CAAC,CAAA;EAClD,KAAA;EAEA,IAAA,OAAO0B,OAAO,CAAA;KACf;EAEDK,EAAAA,IAAIA,CAAChC,OAAO,EAAEC,QAAQ,EAAE;EACtB,IAAA,IAAIgC,QAAQ,GAAGjC,OAAO,CAACkC,sBAAsB,CAAA;EAE7C,IAAA,OAAOD,QAAQ,EAAE;EACf,MAAA,IAAIA,QAAQ,CAACP,OAAO,CAACzB,QAAQ,CAAC,EAAE;UAC9B,OAAO,CAACgC,QAAQ,CAAC,CAAA;EACnB,OAAA;QAEAA,QAAQ,GAAGA,QAAQ,CAACC,sBAAsB,CAAA;EAC5C,KAAA;EAEA,IAAA,OAAO,EAAE,CAAA;KACV;EACD;EACAC,EAAAA,IAAIA,CAACnC,OAAO,EAAEC,QAAQ,EAAE;EACtB,IAAA,IAAIkC,IAAI,GAAGnC,OAAO,CAACoC,kBAAkB,CAAA;EAErC,IAAA,OAAOD,IAAI,EAAE;EACX,MAAA,IAAIA,IAAI,CAACT,OAAO,CAACzB,QAAQ,CAAC,EAAE;UAC1B,OAAO,CAACkC,IAAI,CAAC,CAAA;EACf,OAAA;QAEAA,IAAI,GAAGA,IAAI,CAACC,kBAAkB,CAAA;EAChC,KAAA;EAEA,IAAA,OAAO,EAAE,CAAA;KACV;IAEDC,iBAAiBA,CAACrC,OAAO,EAAE;EACzB,IAAA,MAAMsC,UAAU,GAAG,CACjB,GAAG,EACH,QAAQ,EACR,OAAO,EACP,UAAU,EACV,QAAQ,EACR,SAAS,EACT,YAAY,EACZ,0BAA0B,CAC3B,CAAC9B,GAAG,CAACP,QAAQ,IAAK,CAAA,EAAEA,QAAS,CAAA,qBAAA,CAAsB,CAAC,CAACU,IAAI,CAAC,GAAG,CAAC,CAAA;MAE/D,OAAO,IAAI,CAACE,IAAI,CAACyB,UAAU,EAAEtC,OAAO,CAAC,CAACwB,MAAM,CAACe,EAAE,IAAI,CAACC,mBAAU,CAACD,EAAE,CAAC,IAAIE,kBAAS,CAACF,EAAE,CAAC,CAAC,CAAA;KACrF;IAEDG,sBAAsBA,CAAC1C,OAAO,EAAE;EAC9B,IAAA,MAAMC,QAAQ,GAAGF,WAAW,CAACC,OAAO,CAAC,CAAA;EAErC,IAAA,IAAIC,QAAQ,EAAE;QACZ,OAAOW,cAAc,CAACS,OAAO,CAACpB,QAAQ,CAAC,GAAGA,QAAQ,GAAG,IAAI,CAAA;EAC3D,KAAA;EAEA,IAAA,OAAO,IAAI,CAAA;KACZ;IAED0C,sBAAsBA,CAAC3C,OAAO,EAAE;EAC9B,IAAA,MAAMC,QAAQ,GAAGF,WAAW,CAACC,OAAO,CAAC,CAAA;MAErC,OAAOC,QAAQ,GAAGW,cAAc,CAACS,OAAO,CAACpB,QAAQ,CAAC,GAAG,IAAI,CAAA;KAC1D;IAED2C,+BAA+BA,CAAC5C,OAAO,EAAE;EACvC,IAAA,MAAMC,QAAQ,GAAGF,WAAW,CAACC,OAAO,CAAC,CAAA;MAErC,OAAOC,QAAQ,GAAGW,cAAc,CAACC,IAAI,CAACZ,QAAQ,CAAC,GAAG,EAAE,CAAA;EACtD,GAAA;EACF;;;;;;;;"}