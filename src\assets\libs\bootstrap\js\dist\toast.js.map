{"version": 3, "file": "toast.js", "sources": ["../src/toast.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap toast.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport BaseComponent from './base-component.js'\nimport EventHandler from './dom/event-handler.js'\nimport { enableDismissTrigger } from './util/component-functions.js'\nimport { defineJQueryPlugin, reflow } from './util/index.js'\n\n/**\n * Constants\n */\n\nconst NAME = 'toast'\nconst DATA_KEY = 'bs.toast'\nconst EVENT_KEY = `.${D<PERSON>A_KEY}`\n\nconst EVENT_MOUSEOVER = `mouseover${EVENT_KEY}`\nconst EVENT_MOUSEOUT = `mouseout${EVENT_KEY}`\nconst EVENT_FOCUSIN = `focusin${EVENT_KEY}`\nconst EVENT_FOCUSOUT = `focusout${EVENT_KEY}`\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\n\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_HIDE = 'hide' // @deprecated - kept here only for backwards compatibility\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_SHOWING = 'showing'\n\nconst DefaultType = {\n  animation: 'boolean',\n  autohide: 'boolean',\n  delay: 'number'\n}\n\nconst Default = {\n  animation: true,\n  autohide: true,\n  delay: 5000\n}\n\n/**\n * Class definition\n */\n\nclass Toast extends BaseComponent {\n  constructor(element, config) {\n    super(element, config)\n\n    this._timeout = null\n    this._hasMouseInteraction = false\n    this._hasKeyboardInteraction = false\n    this._setListeners()\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  show() {\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW)\n\n    if (showEvent.defaultPrevented) {\n      return\n    }\n\n    this._clearTimeout()\n\n    if (this._config.animation) {\n      this._element.classList.add(CLASS_NAME_FADE)\n    }\n\n    const complete = () => {\n      this._element.classList.remove(CLASS_NAME_SHOWING)\n      EventHandler.trigger(this._element, EVENT_SHOWN)\n\n      this._maybeScheduleHide()\n    }\n\n    this._element.classList.remove(CLASS_NAME_HIDE) // @deprecated\n    reflow(this._element)\n    this._element.classList.add(CLASS_NAME_SHOW, CLASS_NAME_SHOWING)\n\n    this._queueCallback(complete, this._element, this._config.animation)\n  }\n\n  hide() {\n    if (!this.isShown()) {\n      return\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    const complete = () => {\n      this._element.classList.add(CLASS_NAME_HIDE) // @deprecated\n      this._element.classList.remove(CLASS_NAME_SHOWING, CLASS_NAME_SHOW)\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    }\n\n    this._element.classList.add(CLASS_NAME_SHOWING)\n    this._queueCallback(complete, this._element, this._config.animation)\n  }\n\n  dispose() {\n    this._clearTimeout()\n\n    if (this.isShown()) {\n      this._element.classList.remove(CLASS_NAME_SHOW)\n    }\n\n    super.dispose()\n  }\n\n  isShown() {\n    return this._element.classList.contains(CLASS_NAME_SHOW)\n  }\n\n  // Private\n\n  _maybeScheduleHide() {\n    if (!this._config.autohide) {\n      return\n    }\n\n    if (this._hasMouseInteraction || this._hasKeyboardInteraction) {\n      return\n    }\n\n    this._timeout = setTimeout(() => {\n      this.hide()\n    }, this._config.delay)\n  }\n\n  _onInteraction(event, isInteracting) {\n    switch (event.type) {\n      case 'mouseover':\n      case 'mouseout': {\n        this._hasMouseInteraction = isInteracting\n        break\n      }\n\n      case 'focusin':\n      case 'focusout': {\n        this._hasKeyboardInteraction = isInteracting\n        break\n      }\n\n      default: {\n        break\n      }\n    }\n\n    if (isInteracting) {\n      this._clearTimeout()\n      return\n    }\n\n    const nextElement = event.relatedTarget\n    if (this._element === nextElement || this._element.contains(nextElement)) {\n      return\n    }\n\n    this._maybeScheduleHide()\n  }\n\n  _setListeners() {\n    EventHandler.on(this._element, EVENT_MOUSEOVER, event => this._onInteraction(event, true))\n    EventHandler.on(this._element, EVENT_MOUSEOUT, event => this._onInteraction(event, false))\n    EventHandler.on(this._element, EVENT_FOCUSIN, event => this._onInteraction(event, true))\n    EventHandler.on(this._element, EVENT_FOCUSOUT, event => this._onInteraction(event, false))\n  }\n\n  _clearTimeout() {\n    clearTimeout(this._timeout)\n    this._timeout = null\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Toast.getOrCreateInstance(this, config)\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config](this)\n      }\n    })\n  }\n}\n\n/**\n * Data API implementation\n */\n\nenableDismissTrigger(Toast)\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Toast)\n\nexport default Toast\n"], "names": ["NAME", "DATA_KEY", "EVENT_KEY", "EVENT_MOUSEOVER", "EVENT_MOUSEOUT", "EVENT_FOCUSIN", "EVENT_FOCUSOUT", "EVENT_HIDE", "EVENT_HIDDEN", "EVENT_SHOW", "EVENT_SHOWN", "CLASS_NAME_FADE", "CLASS_NAME_HIDE", "CLASS_NAME_SHOW", "CLASS_NAME_SHOWING", "DefaultType", "animation", "autohide", "delay", "<PERSON><PERSON><PERSON>", "Toast", "BaseComponent", "constructor", "element", "config", "_timeout", "_hasMouseInteraction", "_hasKeyboardInteraction", "_setListeners", "show", "showEvent", "EventHandler", "trigger", "_element", "defaultPrevented", "_clearTimeout", "_config", "classList", "add", "complete", "remove", "_maybeScheduleHide", "reflow", "_queueCallback", "hide", "isShown", "hideEvent", "dispose", "contains", "setTimeout", "_onInteraction", "event", "isInteracting", "type", "nextElement", "relatedTarget", "on", "clearTimeout", "jQueryInterface", "each", "data", "getOrCreateInstance", "TypeError", "enableDismissTrigger", "defineJQueryPlugin"], "mappings": ";;;;;;;;;;;EAAA;EACA;EACA;EACA;EACA;EACA;;;EAOA;EACA;EACA;;EAEA,MAAMA,IAAI,GAAG,OAAO,CAAA;EACpB,MAAMC,QAAQ,GAAG,UAAU,CAAA;EAC3B,MAAMC,SAAS,GAAI,CAAGD,CAAAA,EAAAA,QAAS,CAAC,CAAA,CAAA;EAEhC,MAAME,eAAe,GAAI,CAAWD,SAAAA,EAAAA,SAAU,CAAC,CAAA,CAAA;EAC/C,MAAME,cAAc,GAAI,CAAUF,QAAAA,EAAAA,SAAU,CAAC,CAAA,CAAA;EAC7C,MAAMG,aAAa,GAAI,CAASH,OAAAA,EAAAA,SAAU,CAAC,CAAA,CAAA;EAC3C,MAAMI,cAAc,GAAI,CAAUJ,QAAAA,EAAAA,SAAU,CAAC,CAAA,CAAA;EAC7C,MAAMK,UAAU,GAAI,CAAML,IAAAA,EAAAA,SAAU,CAAC,CAAA,CAAA;EACrC,MAAMM,YAAY,GAAI,CAAQN,MAAAA,EAAAA,SAAU,CAAC,CAAA,CAAA;EACzC,MAAMO,UAAU,GAAI,CAAMP,IAAAA,EAAAA,SAAU,CAAC,CAAA,CAAA;EACrC,MAAMQ,WAAW,GAAI,CAAOR,KAAAA,EAAAA,SAAU,CAAC,CAAA,CAAA;EAEvC,MAAMS,eAAe,GAAG,MAAM,CAAA;EAC9B,MAAMC,eAAe,GAAG,MAAM,CAAC;EAC/B,MAAMC,eAAe,GAAG,MAAM,CAAA;EAC9B,MAAMC,kBAAkB,GAAG,SAAS,CAAA;EAEpC,MAAMC,WAAW,GAAG;EAClBC,EAAAA,SAAS,EAAE,SAAS;EACpBC,EAAAA,QAAQ,EAAE,SAAS;EACnBC,EAAAA,KAAK,EAAE,QAAA;EACT,CAAC,CAAA;EAED,MAAMC,OAAO,GAAG;EACdH,EAAAA,SAAS,EAAE,IAAI;EACfC,EAAAA,QAAQ,EAAE,IAAI;EACdC,EAAAA,KAAK,EAAE,IAAA;EACT,CAAC,CAAA;;EAED;EACA;EACA;;EAEA,MAAME,KAAK,SAASC,aAAa,CAAC;EAChCC,EAAAA,WAAWA,CAACC,OAAO,EAAEC,MAAM,EAAE;EAC3B,IAAA,KAAK,CAACD,OAAO,EAAEC,MAAM,CAAC,CAAA;MAEtB,IAAI,CAACC,QAAQ,GAAG,IAAI,CAAA;MACpB,IAAI,CAACC,oBAAoB,GAAG,KAAK,CAAA;MACjC,IAAI,CAACC,uBAAuB,GAAG,KAAK,CAAA;MACpC,IAAI,CAACC,aAAa,EAAE,CAAA;EACtB,GAAA;;EAEA;IACA,WAAWT,OAAOA,GAAG;EACnB,IAAA,OAAOA,OAAO,CAAA;EAChB,GAAA;IAEA,WAAWJ,WAAWA,GAAG;EACvB,IAAA,OAAOA,WAAW,CAAA;EACpB,GAAA;IAEA,WAAWf,IAAIA,GAAG;EAChB,IAAA,OAAOA,IAAI,CAAA;EACb,GAAA;;EAEA;EACA6B,EAAAA,IAAIA,GAAG;MACL,MAAMC,SAAS,GAAGC,YAAY,CAACC,OAAO,CAAC,IAAI,CAACC,QAAQ,EAAExB,UAAU,CAAC,CAAA;MAEjE,IAAIqB,SAAS,CAACI,gBAAgB,EAAE;EAC9B,MAAA,OAAA;EACF,KAAA;MAEA,IAAI,CAACC,aAAa,EAAE,CAAA;EAEpB,IAAA,IAAI,IAAI,CAACC,OAAO,CAACpB,SAAS,EAAE;QAC1B,IAAI,CAACiB,QAAQ,CAACI,SAAS,CAACC,GAAG,CAAC3B,eAAe,CAAC,CAAA;EAC9C,KAAA;MAEA,MAAM4B,QAAQ,GAAGA,MAAM;QACrB,IAAI,CAACN,QAAQ,CAACI,SAAS,CAACG,MAAM,CAAC1B,kBAAkB,CAAC,CAAA;QAClDiB,YAAY,CAACC,OAAO,CAAC,IAAI,CAACC,QAAQ,EAAEvB,WAAW,CAAC,CAAA;QAEhD,IAAI,CAAC+B,kBAAkB,EAAE,CAAA;OAC1B,CAAA;MAED,IAAI,CAACR,QAAQ,CAACI,SAAS,CAACG,MAAM,CAAC5B,eAAe,CAAC,CAAC;EAChD8B,IAAAA,eAAM,CAAC,IAAI,CAACT,QAAQ,CAAC,CAAA;MACrB,IAAI,CAACA,QAAQ,CAACI,SAAS,CAACC,GAAG,CAACzB,eAAe,EAAEC,kBAAkB,CAAC,CAAA;EAEhE,IAAA,IAAI,CAAC6B,cAAc,CAACJ,QAAQ,EAAE,IAAI,CAACN,QAAQ,EAAE,IAAI,CAACG,OAAO,CAACpB,SAAS,CAAC,CAAA;EACtE,GAAA;EAEA4B,EAAAA,IAAIA,GAAG;EACL,IAAA,IAAI,CAAC,IAAI,CAACC,OAAO,EAAE,EAAE;EACnB,MAAA,OAAA;EACF,KAAA;MAEA,MAAMC,SAAS,GAAGf,YAAY,CAACC,OAAO,CAAC,IAAI,CAACC,QAAQ,EAAE1B,UAAU,CAAC,CAAA;MAEjE,IAAIuC,SAAS,CAACZ,gBAAgB,EAAE;EAC9B,MAAA,OAAA;EACF,KAAA;MAEA,MAAMK,QAAQ,GAAGA,MAAM;QACrB,IAAI,CAACN,QAAQ,CAACI,SAAS,CAACC,GAAG,CAAC1B,eAAe,CAAC,CAAC;QAC7C,IAAI,CAACqB,QAAQ,CAACI,SAAS,CAACG,MAAM,CAAC1B,kBAAkB,EAAED,eAAe,CAAC,CAAA;QACnEkB,YAAY,CAACC,OAAO,CAAC,IAAI,CAACC,QAAQ,EAAEzB,YAAY,CAAC,CAAA;OAClD,CAAA;MAED,IAAI,CAACyB,QAAQ,CAACI,SAAS,CAACC,GAAG,CAACxB,kBAAkB,CAAC,CAAA;EAC/C,IAAA,IAAI,CAAC6B,cAAc,CAACJ,QAAQ,EAAE,IAAI,CAACN,QAAQ,EAAE,IAAI,CAACG,OAAO,CAACpB,SAAS,CAAC,CAAA;EACtE,GAAA;EAEA+B,EAAAA,OAAOA,GAAG;MACR,IAAI,CAACZ,aAAa,EAAE,CAAA;EAEpB,IAAA,IAAI,IAAI,CAACU,OAAO,EAAE,EAAE;QAClB,IAAI,CAACZ,QAAQ,CAACI,SAAS,CAACG,MAAM,CAAC3B,eAAe,CAAC,CAAA;EACjD,KAAA;MAEA,KAAK,CAACkC,OAAO,EAAE,CAAA;EACjB,GAAA;EAEAF,EAAAA,OAAOA,GAAG;MACR,OAAO,IAAI,CAACZ,QAAQ,CAACI,SAAS,CAACW,QAAQ,CAACnC,eAAe,CAAC,CAAA;EAC1D,GAAA;;EAEA;;EAEA4B,EAAAA,kBAAkBA,GAAG;EACnB,IAAA,IAAI,CAAC,IAAI,CAACL,OAAO,CAACnB,QAAQ,EAAE;EAC1B,MAAA,OAAA;EACF,KAAA;EAEA,IAAA,IAAI,IAAI,CAACS,oBAAoB,IAAI,IAAI,CAACC,uBAAuB,EAAE;EAC7D,MAAA,OAAA;EACF,KAAA;EAEA,IAAA,IAAI,CAACF,QAAQ,GAAGwB,UAAU,CAAC,MAAM;QAC/B,IAAI,CAACL,IAAI,EAAE,CAAA;EACb,KAAC,EAAE,IAAI,CAACR,OAAO,CAAClB,KAAK,CAAC,CAAA;EACxB,GAAA;EAEAgC,EAAAA,cAAcA,CAACC,KAAK,EAAEC,aAAa,EAAE;MACnC,QAAQD,KAAK,CAACE,IAAI;EAChB,MAAA,KAAK,WAAW,CAAA;EAChB,MAAA,KAAK,UAAU;EAAE,QAAA;YACf,IAAI,CAAC3B,oBAAoB,GAAG0B,aAAa,CAAA;EACzC,UAAA,MAAA;EACF,SAAA;EAEA,MAAA,KAAK,SAAS,CAAA;EACd,MAAA,KAAK,UAAU;EAAE,QAAA;YACf,IAAI,CAACzB,uBAAuB,GAAGyB,aAAa,CAAA;EAC5C,UAAA,MAAA;EACF,SAAA;EAKF,KAAA;EAEA,IAAA,IAAIA,aAAa,EAAE;QACjB,IAAI,CAACjB,aAAa,EAAE,CAAA;EACpB,MAAA,OAAA;EACF,KAAA;EAEA,IAAA,MAAMmB,WAAW,GAAGH,KAAK,CAACI,aAAa,CAAA;EACvC,IAAA,IAAI,IAAI,CAACtB,QAAQ,KAAKqB,WAAW,IAAI,IAAI,CAACrB,QAAQ,CAACe,QAAQ,CAACM,WAAW,CAAC,EAAE;EACxE,MAAA,OAAA;EACF,KAAA;MAEA,IAAI,CAACb,kBAAkB,EAAE,CAAA;EAC3B,GAAA;EAEAb,EAAAA,aAAaA,GAAG;EACdG,IAAAA,YAAY,CAACyB,EAAE,CAAC,IAAI,CAACvB,QAAQ,EAAE9B,eAAe,EAAEgD,KAAK,IAAI,IAAI,CAACD,cAAc,CAACC,KAAK,EAAE,IAAI,CAAC,CAAC,CAAA;EAC1FpB,IAAAA,YAAY,CAACyB,EAAE,CAAC,IAAI,CAACvB,QAAQ,EAAE7B,cAAc,EAAE+C,KAAK,IAAI,IAAI,CAACD,cAAc,CAACC,KAAK,EAAE,KAAK,CAAC,CAAC,CAAA;EAC1FpB,IAAAA,YAAY,CAACyB,EAAE,CAAC,IAAI,CAACvB,QAAQ,EAAE5B,aAAa,EAAE8C,KAAK,IAAI,IAAI,CAACD,cAAc,CAACC,KAAK,EAAE,IAAI,CAAC,CAAC,CAAA;EACxFpB,IAAAA,YAAY,CAACyB,EAAE,CAAC,IAAI,CAACvB,QAAQ,EAAE3B,cAAc,EAAE6C,KAAK,IAAI,IAAI,CAACD,cAAc,CAACC,KAAK,EAAE,KAAK,CAAC,CAAC,CAAA;EAC5F,GAAA;EAEAhB,EAAAA,aAAaA,GAAG;EACdsB,IAAAA,YAAY,CAAC,IAAI,CAAChC,QAAQ,CAAC,CAAA;MAC3B,IAAI,CAACA,QAAQ,GAAG,IAAI,CAAA;EACtB,GAAA;;EAEA;IACA,OAAOiC,eAAeA,CAAClC,MAAM,EAAE;EAC7B,IAAA,OAAO,IAAI,CAACmC,IAAI,CAAC,YAAY;QAC3B,MAAMC,IAAI,GAAGxC,KAAK,CAACyC,mBAAmB,CAAC,IAAI,EAAErC,MAAM,CAAC,CAAA;EAEpD,MAAA,IAAI,OAAOA,MAAM,KAAK,QAAQ,EAAE;EAC9B,QAAA,IAAI,OAAOoC,IAAI,CAACpC,MAAM,CAAC,KAAK,WAAW,EAAE;EACvC,UAAA,MAAM,IAAIsC,SAAS,CAAE,CAAmBtC,iBAAAA,EAAAA,MAAO,GAAE,CAAC,CAAA;EACpD,SAAA;EAEAoC,QAAAA,IAAI,CAACpC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAA;EACpB,OAAA;EACF,KAAC,CAAC,CAAA;EACJ,GAAA;EACF,CAAA;;EAEA;EACA;EACA;;AAEAuC,4CAAoB,CAAC3C,KAAK,CAAC,CAAA;;EAE3B;EACA;EACA;;AAEA4C,6BAAkB,CAAC5C,KAAK,CAAC;;;;;;;;"}