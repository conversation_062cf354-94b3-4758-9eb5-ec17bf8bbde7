{"version": "5.3.6", "name": "simplebar", "title": "SimpleBar.js", "description": "Scrollbars, simpler.", "files": ["dist", "src", "README.md"], "author": "<PERSON><PERSON> from a fork by <PERSON>", "repository": {"type": "git", "url": "https://github.com/grsmto/simplebar.git", "directory": "packages/simplebar"}, "main": "dist/simplebar.min.js", "module": "dist/simplebar.esm.js", "types": "dist/simplebar.d.ts", "style": "dist/simplebar.min.css", "homepage": "https://grsmto.github.io/simplebar/", "bugs": "https://github.com/grsmto/simplebar/issues", "license": "MIT", "scripts": {"start": "webpack-dev-server --mode=development", "build": "rollup -c && cp src/simplebar.css dist/simplebar.css && cp simplebar.d.ts dist/simplebar.d.ts && minify dist/simplebar.css > dist/simplebar.min.css && webpack --mode=production", "dev": "rollup -c -w", "test": "yarn test:unit && yarn test:e2e", "test:unit": "jest -c jest-unit.config.js", "test:e2e": "env-cmd intern", "version": "yarn build", "precommit": "lint-staged"}, "dependencies": {"@juggle/resize-observer": "^3.3.1", "can-use-dom": "^0.1.0", "core-js": "^3.0.1", "lodash.debounce": "^4.0.8", "lodash.memoize": "^4.1.2", "lodash.throttle": "^4.1.1"}, "devDependencies": {"css-loader": "^0.28.11", "intern": "^4.4.2", "minify": "^3.0.5", "promise": "^8.0.2", "react-select": "^2.4.3", "react-window": "^1.8.1", "style-loader": "^0.21.0"}, "lint-staged": {"*.{js,jsx,json}": ["prettier-eslint --write", "git add"]}, "gitHead": "40b847916bfdd96a46dce6e9af0128b239dc70d5"}