import { Injectable } from '@angular/core';
import { HttpClient,HttpClientModule } from '@angular/common/http';
import { Observable } from 'rxjs';
import {  Agent, Utilisateur } from './utilisateur';

@Injectable({
  providedIn: 'root'
})
export class UtilisateurService {
   private baseURL="http://localhost:8085/auth";
  private baseURL1="http://localhost:8085/api/users";
  constructor(private httpClient:HttpClient) { }
  login(user: { email: string; password: string }): Observable<any> {
    return this.httpClient.post<any>(`${this.baseURL}/login`, user);
  }
  

  register(agent: Agent): Observable<any> {
    return this.httpClient.post(`${this.baseURL}/register`, agent, {
      headers: { 'Content-Type': 'application/json' }
    });
  }
    getUtilisateur(): Observable<Utilisateur[]> {
    return this.httpClient.get<Utilisateur[]>(`${this.baseURL}/AllUsers`);
  }
    getAgents(): Observable<Agent[]> {
    return this.httpClient.get<Agent[]>(`${this.baseURL1}/agents`);
  }
  forgotPassword(email: string) {
    return this.httpClient.post(`${this.baseURL}/forgot-password`, { email }, { responseType: 'text' });
  }
  resetPassword(token: string, newPassword: string) {
    return this.httpClient.post(`${this.baseURL}/reset-password?token=${token}`, { password: newPassword }, { responseType: 'text' });
  }
  
  checkEmailAvailability(email: string): Observable<any> {
    return this.httpClient.get<any>(`${this.baseURL}/check-email?email=${email}`);
  }






}
