// ----------------------------------------------
// SideBar Style
// ----------------------------------------------

.left-sidebar {
  width: $sidebar-width-full;
  border-right: 1px solid var(--bs-border-color);
  flex-shrink: 0;
  background: var(--bs-body-bg);
  z-index: 99;
  transition: 0.2s ease-in;
  position: fixed;
  left: 0;
  right: 0;
  top: -40px; /* 👉 nécessaire pour coller en haut */
  height: 100%;

  .sidebartoggler {
    color: var(--bs-dark-text-emphasis);
  }

  .scroll-sidebar {
    overflow-y: auto;
    padding: $sidebar-spacing-x;
    height: calc(100vh - 310px);
    border-radius: $border-radius;

    .simplebar-track.simplebar-horizontal {
      visibility: hidden !important;
    }
  }
}

.simplebar-scrollbar:before {
  background: rgba(0, 0, 0, 0.5) !important;
}

.brand-logo {
  min-height: $header-height;
  padding: $sidebar-spacing-x;
}

.nav-small-cap {
  margin-top: 24px;
  color: var(--bs-link-color);
  font-size: 12px;
  font-weight: 700;
  padding: 3px 0;
  line-height: 26px;
  text-transform: uppercase;

  .nav-small-cap-icon {
    display: none;
  }
}

.sidebar-nav {
  ul {
    .sidebar-item {
      .sidebar-link {
        display: flex;
        font-size: 16px;
        white-space: nowrap;
        align-items: center;
        line-height: 25px;
        position: relative;
        margin: 0px 0px 2px;
        padding: 10px;
        border-radius: 7px;
        gap: 15px;
        text-decoration: none;
        font-weight: $font-weight-normal;

        span:first-child {
          display: flex;
        }

        .ti {
          flex-shrink: 0;
          font-size: 21px;
        }

        .ti {
          flex-shrink: 0;
          font-size: 21px;
        }

        &:hover {
          background: var(--bs-primary-bg-subtle);
          color: var(--bs-primary);

          &.has-arrow::after {
            border-color: var(--bs-primary);
          }
        }
      }

      .sidebar-link.active {
        &:hover {
          &.has-arrow::after {
            border-color: var(--bs-white);
          }
        }
      }

      .first-level {
        .sidebar-item {
          transition: all 0.4s ease-in-out;
          border-bottom: 0;

          .sidebar-link {
            &:hover {
              background-color: transparent;
              color: var(--bs-primary);
            }
          }

          .sidebar-link.active {
            &.has-arrow::after {
              border-color: var(--bs-primary);
            }
          }

          &>.sidebar-link {
            padding: 8px 10px;
            border-radius: 7px;
            font-size: 16px;
            gap: 23px;

            .sidebar-icon {
              flex-shrink: 0;
              margin-left: 12px;
              margin-right: 35px;
              width: 14px;
              height: 14px;
            }
          }

          &:last-child {
            margin-bottom: 16px;
          }
        }

        .sidebar-link {
          .ti {
            font-size: 7px;
          }
        }
      }

      .first-level {
        .sidebar-item {
          .sidebar-link.active {
            background-color: transparent !important;
            color: var(--bs-primary) !important;
          }
        }
      }

      .two-level {
        .sidebar-item {
          .sidebar-link {
            padding: 8px 10px 8px 45px;
          }
        }
      }

      .three-level {
        .sidebar-item {
          .sidebar-link {
            padding: 8px 10px 8px 60px;
          }
        }
      }

      &.selected>.sidebar-link.active,
      &.selected>.sidebar-link,
      >.sidebar-link.active {
        background-color: var(--bs-primary);
        color: var(--bs-white);
      }
    }

    .sidebar-item.selected {
      .sidebar-link.has-arrow {
        &::after {
          border-color: var(--bs-white);
        }
      }
    }
  }

  .sidebar-list {
    .sidebar-list-item {
      padding: 8px 0;
    }
  }
}

.collapse.in {
  display: block;
}



// Down arrow

.sidebar-nav .has-arrow {
  position: relative;

  &::after {
    position: absolute;
    content: "";
    width: 7px;
    height: 7px;
    border-width: 1px 0 0 1px;
    border-style: solid;
    border-color: var(--bs-dark-text-emphasis);
    margin-left: 10px;
    -webkit-transform: rotate(135deg) translate(0, -50%);
    -ms-transform: rotate(135deg) translate(0, -50%);
    -o-transform: rotate(135deg) translate(0, -50%);
    transform: rotate(135deg) translate(0, -50%);
    -webkit-transform-origin: top;
    -ms-transform-origin: top;
    -o-transform-origin: top;
    transform-origin: top;
    top: 22px;
    right: 15px;
    -webkit-transition: all 0.3s ease-out;
    -o-transition: all 0.3s ease-out;
    transition: all 0.3s ease-out;
  }
}

.sidebar-nav li.active>.has-arrow::after,
.sidebar-nav li>.has-arrow.active::after,
.sidebar-nav .has-arrow[aria-expanded="true"]::after {
  top: 18px;
  margin-top: 1px;
  border-color: var(--bs-white);
  -webkit-transform: rotate(-135deg) translate(0, -50%);
  -ms-transform: rotate(-135deg) translate(0, -50%);
  -o-transform: rotate(-135deg) translate(0, -50%);
  transform: rotate(-135deg) translate(0, -50%);
}