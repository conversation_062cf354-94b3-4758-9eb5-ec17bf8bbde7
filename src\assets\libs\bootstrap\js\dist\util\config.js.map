{"version": 3, "file": "config.js", "sources": ["../../src/util/config.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap util/config.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport Manipulator from '../dom/manipulator.js'\nimport { isElement, toType } from './index.js'\n\n/**\n * Class definition\n */\n\nclass Config {\n  // Getters\n  static get Default() {\n    return {}\n  }\n\n  static get DefaultType() {\n    return {}\n  }\n\n  static get NAME() {\n    throw new Error('You have to implement the static method \"NAME\", for each component!')\n  }\n\n  _getConfig(config) {\n    config = this._mergeConfigObj(config)\n    config = this._configAfterMerge(config)\n    this._typeCheckConfig(config)\n    return config\n  }\n\n  _configAfterMerge(config) {\n    return config\n  }\n\n  _mergeConfigObj(config, element) {\n    const jsonConfig = isElement(element) ? Manipulator.getDataAttribute(element, 'config') : {} // try to parse\n\n    return {\n      ...this.constructor.Default,\n      ...(typeof jsonConfig === 'object' ? jsonConfig : {}),\n      ...(isElement(element) ? Manipulator.getDataAttributes(element) : {}),\n      ...(typeof config === 'object' ? config : {})\n    }\n  }\n\n  _typeCheckConfig(config, configTypes = this.constructor.DefaultType) {\n    for (const [property, expectedTypes] of Object.entries(configTypes)) {\n      const value = config[property]\n      const valueType = isElement(value) ? 'element' : toType(value)\n\n      if (!new RegExp(expectedTypes).test(valueType)) {\n        throw new TypeError(\n          `${this.constructor.NAME.toUpperCase()}: Option \"${property}\" provided type \"${valueType}\" but expected type \"${expectedTypes}\".`\n        )\n      }\n    }\n  }\n}\n\nexport default Config\n"], "names": ["Config", "<PERSON><PERSON><PERSON>", "DefaultType", "NAME", "Error", "_getConfig", "config", "_mergeConfigObj", "_configAfterMerge", "_typeCheckConfig", "element", "jsonConfig", "isElement", "Manipulator", "getDataAttribute", "constructor", "getDataAttributes", "configTypes", "property", "expectedTypes", "Object", "entries", "value", "valueType", "toType", "RegExp", "test", "TypeError", "toUpperCase"], "mappings": ";;;;;;;;;;;EAAA;EACA;EACA;EACA;EACA;EACA;;;EAKA;EACA;EACA;;EAEA,MAAMA,MAAM,CAAC;EACX;IACA,WAAWC,OAAOA,GAAG;EACnB,IAAA,OAAO,EAAE,CAAA;EACX,GAAA;IAEA,WAAWC,WAAWA,GAAG;EACvB,IAAA,OAAO,EAAE,CAAA;EACX,GAAA;IAEA,WAAWC,IAAIA,GAAG;EAChB,IAAA,MAAM,IAAIC,KAAK,CAAC,qEAAqE,CAAC,CAAA;EACxF,GAAA;IAEAC,UAAUA,CAACC,MAAM,EAAE;EACjBA,IAAAA,MAAM,GAAG,IAAI,CAACC,eAAe,CAACD,MAAM,CAAC,CAAA;EACrCA,IAAAA,MAAM,GAAG,IAAI,CAACE,iBAAiB,CAACF,MAAM,CAAC,CAAA;EACvC,IAAA,IAAI,CAACG,gBAAgB,CAACH,MAAM,CAAC,CAAA;EAC7B,IAAA,OAAOA,MAAM,CAAA;EACf,GAAA;IAEAE,iBAAiBA,CAACF,MAAM,EAAE;EACxB,IAAA,OAAOA,MAAM,CAAA;EACf,GAAA;EAEAC,EAAAA,eAAeA,CAACD,MAAM,EAAEI,OAAO,EAAE;EAC/B,IAAA,MAAMC,UAAU,GAAGC,kBAAS,CAACF,OAAO,CAAC,GAAGG,WAAW,CAACC,gBAAgB,CAACJ,OAAO,EAAE,QAAQ,CAAC,GAAG,EAAE,CAAC;;MAE7F,OAAO;EACL,MAAA,GAAG,IAAI,CAACK,WAAW,CAACd,OAAO;QAC3B,IAAI,OAAOU,UAAU,KAAK,QAAQ,GAAGA,UAAU,GAAG,EAAE;EACpD,MAAA,IAAIC,kBAAS,CAACF,OAAO,CAAC,GAAGG,WAAW,CAACG,iBAAiB,CAACN,OAAO,CAAC,GAAG,EAAE;QACpE,IAAI,OAAOJ,MAAM,KAAK,QAAQ,GAAGA,MAAM,GAAG,EAAE;OAC7C,CAAA;EACH,GAAA;IAEAG,gBAAgBA,CAACH,MAAM,EAAEW,WAAW,GAAG,IAAI,CAACF,WAAW,CAACb,WAAW,EAAE;EACnE,IAAA,KAAK,MAAM,CAACgB,QAAQ,EAAEC,aAAa,CAAC,IAAIC,MAAM,CAACC,OAAO,CAACJ,WAAW,CAAC,EAAE;EACnE,MAAA,MAAMK,KAAK,GAAGhB,MAAM,CAACY,QAAQ,CAAC,CAAA;EAC9B,MAAA,MAAMK,SAAS,GAAGX,kBAAS,CAACU,KAAK,CAAC,GAAG,SAAS,GAAGE,eAAM,CAACF,KAAK,CAAC,CAAA;QAE9D,IAAI,CAAC,IAAIG,MAAM,CAACN,aAAa,CAAC,CAACO,IAAI,CAACH,SAAS,CAAC,EAAE;UAC9C,MAAM,IAAII,SAAS,CAChB,CAAA,EAAE,IAAI,CAACZ,WAAW,CAACZ,IAAI,CAACyB,WAAW,EAAG,aAAYV,QAAS,CAAA,iBAAA,EAAmBK,SAAU,CAAuBJ,qBAAAA,EAAAA,aAAc,IAChI,CAAC,CAAA;EACH,OAAA;EACF,KAAA;EACF,GAAA;EACF;;;;;;;;"}