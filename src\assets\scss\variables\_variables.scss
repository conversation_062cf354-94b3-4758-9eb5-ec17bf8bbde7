// ----------------------------------------------
// Variables Style
// ----------------------------------------------


// colors
$white: #fff !default;
$gray-100: #f6f8fb !default;
$gray-200: #ecf0f2 !default;
$gray-300: #f1f9ff !default;  
$gray-400: #ced4da !default;
$gray-500: #546269 !default;
$gray-600: #6c757d !default;
$gray-700: #4f5467 !default;
$gray-800: #11142d !default;
$gray-900: #223640 !default;
$black: #000 !default;

// Color
$blue: #1e4db7 !default;
$indigo: #6610f2 !default;
$purple: #7460ee !default;
$pink: #e83e8c !default;
$red: #fc4b6c !default;
$orange: #fb8c00 !default;
$yellow: #fdc90f !default;
$green: #39cb7f !default;
$teal: #20c997 !default;
$cyan: #0bb2fb !default;

$dark-transparent: rgba(0, 0, 0, 0.05);

// Main Colors
$primary: $blue !default;
$secondary: #1a97f5 !default;
$success: $green !default;
$info: $cyan !default;
$warning: $yellow !default;
$danger: $red !default;
$light: $gray-100 !default;
$muted: #777e89 !default;
$dark: $gray-800 !default;
$dark-light: $gray-800 !default;
$light-gray: $gray-100 !default;

$light-indigo: #ebf3fe;

// light colors
$primary-bg-subtle: #ddebff !default;
$secondary-bg-subtle: #1a97f51a !default;
$success-bg-subtle: #ebfaf2 !default;
$info-bg-subtle: #e6f4ff !default;
$warning-bg-subtle: #fff4e5 !default;
$danger-bg-subtle: #fdf3f5 !default;
$light-bg-subtle: $gray-100 !default;
$dark-bg-subtle: $gray-800 !default;

$primary-text-emphasis: shade-color($primary, 60%) !default;
$secondary-text-emphasis: shade-color($secondary, 60%) !default;
$success-text-emphasis: shade-color($success, 60%) !default;
$info-text-emphasis: shade-color($info, 60%) !default;
$warning-text-emphasis: shade-color($warning, 60%) !default;
$danger-text-emphasis: shade-color($danger, 60%) !default;
$light-text-emphasis: $gray-100 !default;
$dark-text-emphasis: $gray-800 !default;


// Theme Colorss
$theme-colors: (
  "primary": $primary,
  "secondary": $secondary,
  "success": $success,
  "info": $info,
  "warning": $warning,
  "danger": $danger,
  "light": $light,
  "dark": $dark,
  "muted": $muted,
  "indigo": $indigo,
  "light-indigo": $light-indigo,
) !default;

// Spacer
$spacer: 1rem !default;
$spacers: (
  0: 0,
  1: $spacer * 0.25,
  2: $spacer * 0.5,
  3: $spacer,
  4: $spacer * 1.5,
  5: $spacer * 3,
  6: 12px,
  7: 30px,
  8: 10px,
  9: 20px,
  10: 18px,
  ) !default;

// Common
$min-contrast-ratio: 1.7 !default;

// Margins
$enable-negative-margins: true !default;
$enable-shadows: true !default;

// Global
$text-muted: $muted !default;
$grid-gutter-width: 24px !default;

// Font Weight
$font-weight-lighter: 300 !default;
$font-weight-light: 300 !default;
$font-weight-normal: 400 !default;
$font-weight-bold: 500 !default;
$font-weight-bolder: 700 !default;
$font-weight-base: $font-weight-normal !default;

// Buttons
$btn-padding-y: 8px !default;
$btn-padding-x: 14px !default;
$btn-font-weight: 500 !default;
$btn-border-radius: 7px !default;
$btn-box-shadow: unset !default;
$btn-font-size: 14 !default;
$btn-border-radius-lg: 9px !default;

// Font Family
$font-family-sans-serif: "DM Sans", sans-serif !default;
$font-size-base: 0.9375rem !default;

// Font Sizes
$font-sizes: (
  1: 10px,
  2: 12px,
  3: 14px,
  4: 16px,
  5: 18px,
  6: 20px,
  7: 24px,
  8: 30px,
  9: 36px,
  10: 40px,
  11: 13px,
  12: 15px,
  14: 48px,
  ) !default;

// Line Heght
$line-height-base: 1.5 !default;
$line-height-sm: 1.25 !default;
$line-height-lg: 2 !default;

// Body
$body-bg: $white !default;
$body-color: #67757c !default;

// Heading
$headings-color: $dark !default;
$headings-font-weight: 500 !default;

// Border Color
$border-color: #ecf0f2 !default;

// Box Shadow
$box-shadow: 0px 15px 30px rgba(0, 0, 0, 0.12) !default;
$box-shadow-sm: 0 0.125rem 0.25rem rgba($black, 0.075) !default;
$box-shadow-lg: 0 1rem 3rem rgba($black, 0.175) !default;
$box-shadow-inset: unset !default;

// Card
$card-title-color: $dark !default;
$card-subtitle-color: $muted !default;
$card-spacer-y: 30px !default;
$card-spacer-x: 30px !default;
$card-border-width: 1px !default;
$card-border-color: $border-color !default;
$card-border-radius: $btn-border-radius;

// Border Radius
$border-radius: 7px !default;
$border-radius-sm: 5px !default;
$border-radius-lg: 15px !default;
$border-radius-xl: 12px !default;

// Badges
$badge-font-size: 14px !default;
$badge-font-weight: $font-weight-normal !default;
$badge-padding-y: 5px !default;
$badge-padding-x: 10px !default;
$badge-border-radius: 4px;


// Table
$table-color: #777e89;
$table-cell-padding-y: 16px !default;
$table-cell-padding-x: 16px !default;
$table-th-font-weight: 600 !default;
$table-striped-bg: $gray-200 !default;
$table-hover-bg: $gray-100 !default;
$table-border-color: $border-color !default;


// Input
$form-label-color: $gray-800;
$form-label-font-weight: 600;
$form-select-indicator-padding: 38px;
$form-select-border-radius: 7px !default;
$form-select-box-shadow: unset !default;
$form-select-border-color: #dfe5ef !default;
$input-border-radius: 7px !default;
$input-group-addon-bg: $gray-200 !default;
$input-group-addon-border-color: $light !default;
$input-border-color: #dfe5ef !default;
$input-bg: transparent !default;
$input-color: $muted !default;
$input-padding-y: 8px;
$input-padding-x: 16px;
$form-check-input-width: 1.313em;
$form-check-min-height: 1.313em;
$form-check-input-border: 1.25px solid #bdc3cd;
$form-check-input-checked-bg-color:var(--bs-primary);

// Dropdown
$dropdown-border-radius: 7px !default;
$dropdown-width: 360px !default;
$dropdown-border-width: 0 !default;
$dropdown-item-padding-y: 10px !default;
$dropdown-link-hover-bg: $gray-100 !default;
$dropdown-link-active-bg: $gray-100;
$dropdown-link-active-color: $dark;
$dropdown-box-shadow: rgb(145 158 171 / 20%) 0px 0px 2px 0px,
  rgb(145 158 171 / 12%) 0px 12px 24px -4px;
$link-color: $dark !default;

$link-hover-color: $primary !default;

// Modal
$modal-content-border-width: 0 !default;