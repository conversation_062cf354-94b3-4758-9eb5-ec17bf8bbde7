/* ===== DESIGN SHARP & MODERNE ===== */

/* Header principal avec le même bleu que le bouton "Nouvelle Affectation" */
.welcome-header {
  background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
  color: white;
  padding: 40px 50px;
  border-radius: 20px;
  margin-bottom: 30px;
  box-shadow: 0 10px 30px rgba(37, 99, 235, 0.3);
  position: relative;
  overflow: hidden;
}

.welcome-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(255,255,255,0.1) 0%, transparent 100%);
  pointer-events: none;
}

.welcome-header h1 {
  font-size: 32px;
  font-weight: 800;
  margin: 0 0 12px 0;
  color: white;
  letter-spacing: -0.5px;
  text-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.welcome-header p {
  font-size: 18px;
  margin: 0;
  opacity: 0.95;
  font-weight: 300;
}
/* Cards de statistiques modernes */
.stats-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 25px;
  padding: 0 20px;
  margin-top: -15px;
}

.stat-card {
  background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 20px;
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.08),
    0 2px 8px rgba(0, 0, 0, 0.04);
  padding: 30px 35px;
  text-align: center;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  border: 1px solid rgba(255, 255, 255, 0.8);
  position: relative;
  overflow: hidden;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
}

.stat-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.12),
    0 8px 16px rgba(0, 0, 0, 0.08);
}

.stat-label {
  font-size: 15px;
  color: #64748b;
  margin-bottom: 12px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.stat-value {
  font-size: 32px;
  font-weight: 800;
  color: #1e293b;
  margin: 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}
/* Header container moderne */
.header-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
  padding: 35px 40px;
  border-radius: 20px;
  margin-bottom: 30px;
  border: 1px solid rgba(226, 232, 240, 0.8);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.04);
}

.header-text h2 {
  margin: 0;
  font-size: 32px;
  color: #1e293b;
  font-weight: 800;
  letter-spacing: -0.5px;
  background: linear-gradient(135deg, #1e293b 0%, #475569 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.header-text p {
  margin: 8px 0 0 0;
  color: #64748b;
  font-size: 16px;
  font-weight: 400;
}

/* Bouton identique au dashboard */
.add-user-btn {
  background-color: #0051ff;
  color: white;
  padding: 10px 16px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  transition: background-color 0.2s ease;
}

.add-user-btn:hover {
  background-color: #1f2937;
}

.add-user-btn .icon {
  margin-right: 8px;
  font-size: 16px;
}
/* ===== MODALS CLASSIQUES ===== */

/* Modal background */
.modal {
  display: none;
  position: fixed;
  z-index: 1000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  overflow: auto;
  background-color: rgba(0,0,0,0.4);
}

.modal.show {
  display: flex;
  justify-content: center;
  align-items: center;
}

/* Modal content */
.modal-content {
  background-color: #fff;
  padding: 30px;
  border-radius: 10px;
  width: 500px;
  max-width: 90%;
  box-shadow: 0 5px 15px rgba(0,0,0,0.3);
  position: relative;
  animation: fadeIn 0.3s ease;
}

/* Animation */
@keyframes fadeIn {
  from {opacity: 0;}
  to {opacity: 1;}
}

/* Close button */
.close {
  color: #aaa;
  position: absolute;
  top: 15px;
  right: 20px;
  font-size: 28px;
  font-weight: bold;
  cursor: pointer;
}

.close:hover {
  color: #000;
}
/* ===== FORMULAIRES CLASSIQUES ===== */

/* Form inputs */
.modal-content input,
.modal-content select,
.modal-content textarea {
  width: 100%;
  padding: 10px 12px;
  margin: 10px 0;
  font-size: 14px;
  border: 1px solid #ddd;
  border-radius: 6px;
  outline: none;
  font-family: inherit;
  box-sizing: border-box;
}

.modal-content input:focus,
.modal-content select:focus,
.modal-content textarea:focus {
  border-color: #2563eb;
  box-shadow: 0 0 0 2px rgba(37, 99, 235, 0.2);
}

/* Labels */
.form-label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  color: #333;
  font-size: 14px;
}
/* ===== RECHERCHE MODERNE ===== */

/* Wrapper de recherche élégant */
.search-wrapper {
  background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
  padding: 30px;
  border-radius: 20px;
  border: 1px solid rgba(226, 232, 240, 0.8);
  max-width: 1200px;
  margin: 0 auto 30px auto;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.06);
}

/* Champ de recherche avec icône */
.custom-search {
  position: relative;
  width: 100%;
}

.custom-search input {
  width: 100%;
  padding: 16px 20px 16px 50px;
  border: 2px solid #e2e8f0;
  border-radius: 16px;
  background: rgba(255, 255, 255, 0.9);
  font-size: 16px;
  color: #1e293b;
  outline: none;
  box-sizing: border-box;
  transition: all 0.3s ease;
  font-weight: 400;
}

.custom-search input:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
  background: rgba(255, 255, 255, 1);
  transform: translateY(-1px);
}

.custom-search input::placeholder {
  color: #94a3b8;
  font-weight: 400;
}

/* Icône de recherche moderne */
.icon-search {
  position: absolute;
  top: 50%;
  left: 18px;
  transform: translateY(-50%);
  width: 20px;
  height: 20px;
  background-image: url('data:image/svg+xml;utf8,<svg fill="%2394a3b8" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path d="M505 442.7L405.3 343c28.4-34.9 45.7-79 45.7-127C451 103.5 349.5 2 225.5 2S0 103.5 0 216.5 103.5 431 216.5 431c48 0 92.1-17.3 127-45.7l99.7 99.7c4.6 4.6 10.6 7 16.7 7s12.1-2.3 16.7-7c9.3-9.3 9.3-24.4 0-33.7zM216.5 366c-82.6 0-150-67.4-150-150s67.4-150 150-150 150 67.4 150 150-67.4 150-150 150z"/></svg>');
  background-repeat: no-repeat;
  background-size: 20px 20px;
  pointer-events: none;
  transition: all 0.3s ease;
}
/* ===== BOUTONS CLASSIQUES ===== */

/* Boutons du formulaire */
.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #eee;
}

.btn {
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.btn-primary {
  background-color: #2563eb;
  color: white;
}

.btn-primary:hover {
  background-color: #1d4ed8;
}

.btn-primary:disabled {
  background-color: #9ca3af;
  cursor: not-allowed;
}

.btn-secondary {
  background-color: #6b7280;
  color: white;
}

.btn-secondary:hover {
  background-color: #4b5563;
}

/* ===== NOTIFICATIONS CLASSIQUES ===== */

.simple-notification {
  width: 100%;
  padding: 12px 20px;
  margin-bottom: 15px;
  text-align: center;
  font-weight: 500;
  font-size: 14px;
  border-radius: 6px;
}

.simple-notification.success {
  background-color: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.simple-notification.error {
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}










/* ===== SÉLECTION D'ÉQUIPEMENTS MODERNE ===== */

/* Recherche d'équipements */
.equipment-search {
  margin-bottom: 20px;
}

.search-input {
  width: 100%;
  padding: 14px 18px;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  font-size: 15px;
  outline: none;
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.9);
}

.search-input:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
  background: rgba(255, 255, 255, 1);
  transform: translateY(-1px);
}

.search-input::placeholder {
  color: #94a3b8;
}

/* Container des équipements avec scroll moderne */
.equipment-selection {
  max-height: 300px;
  overflow-y: auto;
  padding: 12px;
  background: linear-gradient(145deg, #f8fafc 0%, #ffffff 100%);
  border-radius: 16px;
  border: 2px solid #e2e8f0;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.02);
}

/* Scrollbar moderne */
.equipment-selection::-webkit-scrollbar {
  width: 10px;
}

.equipment-selection::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 8px;
}

.equipment-selection::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #cbd5e1 0%, #94a3b8 100%);
  border-radius: 8px;
  border: 2px solid #f1f5f9;
}

.equipment-selection::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #94a3b8 0%, #64748b 100%);
}

/* Item d'équipement avec animation subtile */
.equipment-item {
  position: relative;
  margin-bottom: 10px;
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s ease;
  cursor: pointer;
  border: 1px solid #e2e8f0;
  background: white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.equipment-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-color: #2563eb;
}

.equipment-item:last-child {
  margin-bottom: 0;
}

/* Checkbox invisible pour l'accessibilité */
.equipment-checkbox {
  position: absolute;
  opacity: 0;
  cursor: pointer;
  width: 100%;
  height: 100%;
  margin: 0;
}

/* Label de l'équipement */
.equipment-label {
  display: block;
  padding: 20px 24px;
  cursor: pointer;
  margin: 0;
  position: relative;
  transition: all 0.3s ease;
}

/* Layout de l'information */
.equipment-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 16px;
}

.equipment-main {
  display: flex;
  align-items: center;
  flex: 1;
  min-width: 0;
}

/* Icône simple en bleu */
.equipment-icon {
  width: 40px;
  height: 40px;
  background-color: #2563eb;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  font-size: 16px;
  color: white;
  flex-shrink: 0;
  transition: all 0.3s ease;
}

.equipment-item:hover .equipment-icon {
  background-color: #1d4ed8;
  transform: scale(1.05);
}

/* Texte de l'équipement */
.equipment-text {
  flex: 1;
  min-width: 0;
}

.equipment-name {
  font-weight: 700;
  color: #1e293b;
  font-size: 16px;
  margin-bottom: 4px;
  line-height: 1.3;
  letter-spacing: -0.2px;
}

.equipment-details {
  color: #64748b;
  font-size: 13px;
  line-height: 1.4;
  font-weight: 400;
}
/* Status et checkmark */
.equipment-status {
  display: flex;
  align-items: center;
  gap: 12px;
}

/* Badge de statut moderne */
.status-badge {
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.8px;
  border: 2px solid transparent;
  transition: all 0.3s ease;
}

.status-actif {
  background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%);
  color: #166534;
  border-color: #22c55e;
}

.status-maintenance {
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
  color: #92400e;
  border-color: #f59e0b;
}

.status-hors-service {
  background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
  color: #991b1b;
  border-color: #ef4444;
}

/* Checkmark simple en bleu avec animation subtile */
.equipment-checkmark {
  width: 20px;
  height: 20px;
  border: 2px solid #cbd5e1;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  background-color: white;
}

.equipment-checkmark::after {
  content: '✓';
  font-size: 12px;
  font-weight: bold;
  color: white;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.equipment-checkmark.checked {
  background-color: #2563eb;
  border-color: #2563eb;
  transform: scale(1.1);
}

.equipment-checkmark.checked::after {
  opacity: 1;
}

/* Message quand aucun équipement */
.no-equipment-message {
  text-align: center;
  padding: 40px 20px;
  color: #64748b;
  font-style: italic;
  font-size: 16px;
}

.no-equipment-message p {
  margin: 0;
  font-weight: 500;
}
/* ===== FIN DU CSS MODERNE ===== */

/* Responsive design pour mobile */
@media (max-width: 768px) {
  .welcome-header {
    padding: 30px 25px;
  }

  .welcome-header h1 {
    font-size: 28px;
  }

  .header-container {
    flex-direction: column;
    gap: 20px;
    text-align: center;
  }

  .stats-container {
    grid-template-columns: 1fr;
    padding: 0 10px;
  }

  .modal-content {
    width: 95vw;
    padding: 30px 20px;
  }

  .equipment-item {
    margin-bottom: 8px;
  }

  .equipment-label {
    padding: 16px;
  }

  .equipment-icon {
    width: 40px;
    height: 40px;
    font-size: 16px;
  }
}

.welcome-header {
  background: linear-gradient(to right, #2563eb, #1e40af); /* bleu dégradé */
  color: white;
  padding: 30px 40px;
  border-radius: 12px;
  margin-bottom: 20px;
}

.welcome-header h1 {
  font-size: 28px;
  font-weight: bold;
  margin: 0 0 8px 0;
  color: white;
}

.welcome-header p {
  font-size: 16px;
  margin: 0;
  opacity: 0.9;
}
.stats-container {
  display: flex;
  justify-content: space-around;
  flex-wrap: wrap;
  padding: 20px;
  gap: 20px;
}

.stat-card {
  background-color: #ffffff;
  border-radius: 15px;
  box-shadow: 0 0 15px rgba(0, 0, 0, 0.05);
  padding: 20px 30px;
  text-align: center;
  flex: 1 1 200px;
  max-width: 250px;
  transition: transform 0.2s ease;
}

.stat-card:hover {
  transform: translateY(-5px);
}

.stat-label {
  font-size: 14px;
  color: #000000; /* gris foncé */
  margin-bottom: 10px;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #111827; /* noir bleuté */
}
.header-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #f9fafb; /* fond très clair */
  padding: 20px 30px;
  border-radius: 10px;
  margin-bottom: 20px;
}

.header-text h2 {
  margin: 0;
  font-size: 28px;
  color: #111827; /* noir bleuté */
  font-weight: 700;
}

.header-text p {
  margin: 5px 0 0 0;
  color: #6b7280; /* gris foncé */
  font-size: 14px;
}

.add-user-btn {
  background-color: #0051ff; /* bouton noir bleuté */
  color: white;
  padding: 10px 16px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  transition: background-color 0.2s ease;
}

.add-user-btn:hover {
  background-color: #1f2937;
}

.add-user-btn .icon {
  margin-right: 8px;
  font-size: 16px;
}
/* Modal background */
.modal {
  display: none; /* caché par défaut */
  position: fixed;
  z-index: 1000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  overflow: auto;
  background-color: rgba(0,0,0,0.4); /* fond semi-transparent */
}

/* Modal box */
.modal-content {
  background-color: #fff;
  margin: 10% auto;
  padding: 20px;
  border-radius: 10px;
  width: 400px;
  max-width: 90%;
  box-shadow: 0 5px 15px rgba(0,0,0,0.3);
  position: relative;
  animation: fadeIn 0.3s ease;
}

/* Animation */
@keyframes fadeIn {
  from {opacity: 0;}
  to {opacity: 1;}
}

/* Close button */
.close {
  color: #aaa;
  position: absolute;
  top: 12px;
  right: 16px;
  font-size: 28px;
  font-weight: bold;
  cursor: pointer;
}

.close:hover {
  color: #000;
}

/* Form styles */
.modal-content input {
  width: 100%;
  margin: 10px 0;
  padding: 10px;
  border-radius: 6px;
  border: 10px solid #000000;

}

.modal-content button[type="submit"] {
  background-color: #111827;
  color: white;
  border: none;
  padding: 10px 15px;
  border-radius: 6px;
  cursor: pointer;
}

.modal-content button[type="submit"]:hover {
  background-color: #1f2937;
}
.modal {
  display: none;
  position: fixed;
  z-index: 1000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.4);
  justify-content: center;
  align-items: center;
}

.modal.show {
  display: flex;
}

.modal-content {
  background-color: white;
  padding: 20px;
  border-radius: 10px;
  width: 400px;
  max-width: 90%;
  position: relative;
}

.close {
  position: absolute;
  top: 10px;
  right: 15px;
  font-size: 24px;
  cursor: pointer;
}
.stats-container {
  display: flex;
  justify-content: space-around;
  flex-wrap: wrap;
  padding: 20px;
  gap: 20px;

  /* ↓↓↓ ajoute cette ligne ↓↓↓ */
  margin-top: -30px; /* ajuste à ta convenance */
}
select {
  width: 100%;
  max-width: 400px; /* adapte selon besoin */
  padding: 10px 15px;
  border: 1.5px solid #ccc;
  border-radius: 6px;
  background-color: #fff;
  font-size: 1rem;
  color: #333;
  appearance: none; /* enlève style par défaut navigateur */
  cursor: pointer;
  transition: border-color 0.3s ease;
}

/* Ajouter une petite flèche personnalisée */
select {
  background-image: url("data:image/svg+xml;charset=US-ASCII,%3Csvg width='14' height='8' viewBox='0 0 14 8' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath fill='%23333' d='M7 8L0 0h14L7 8z'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 15px center;
  background-size: 14px 8px;
  margin-bottom: 20px; /* espace sous chaque select */
}

/* Au focus, changer la bordure */
select:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 5px rgba(0,123,255,0.5);
}

/* Survol */
select:hover {
  border-color: #888;
}
/* Boîte blanche autour du champ */
.search-wrapper {
  background-color: #ffffff;
  padding: 20px;
  border-radius: 12px;
  border: 1px solid #eee;
  max-width: 1200px;
  margin: 0 auto; /* centre la boîte si besoin */
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05); /* ombre douce */
  margin-top: -20px;
}

/* Champ de recherche */
.custom-search {
  position: relative;
  width: 100%;
}

.custom-search input {
  width: 100%;
  padding: 10px 12px 10px 40px;
  border: 1px solid #dcdcdc;
  border-radius: 8px;
  background-color: #fff;
  font-size: 14px;
  color: #333;
  outline: none;
  box-sizing: border-box;
  transition: border-color 0.2s ease;
}

.custom-search input:focus {
  border-color: #bbb;
}

/* Icône loupe à gauche */
.icon-search {
  position: absolute;
  top: 50%;
  left: 12px;
  transform: translateY(-50%);
  width: 16px;
  height: 16px;
  background-image: url('data:image/svg+xml;utf8,<svg fill="%23999" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path d="M505 442.7L405.3 343c28.4-34.9 45.7-79 45.7-127C451 103.5 349.5 2 225.5 2S0 103.5 0 216.5 103.5 431 216.5 431c48 0 92.1-17.3 127-45.7l99.7 99.7c4.6 4.6 10.6 7 16.7 7s12.1-2.3 16.7-7c9.3-9.3 9.3-24.4 0-33.7zM216.5 366c-82.6 0-150-67.4-150-150s67.4-150 150-150 150 67.4 150 150-67.4 150-150 150z"/></svg>');
  background-repeat: no-repeat;
  background-size: 16px 16px;
  pointer-events: none;
}
/* Modal background */
.modal {
  position: fixed;
  top: 0; left: 0; right: 0; bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 999;
  visibility: hidden;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.modal.show {
  visibility: visible;
  opacity: 1;
}

/* Modal content box */
.modal-content {
  background: white;
  padding: 24px;
  border-radius: 12px;
  width: 500px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  font-family: 'Segoe UI', sans-serif;
  position: relative;
}

/* Close button */
.close {
  position: absolute;
  top: 16px;
  right: 16px;
  font-size: 20px;
  cursor: pointer;
  color: #6b7280;
}

/* Form inputs */
.modal-content input,
.modal-content select {
  width: 100%;
  padding: 10px 12px;
  margin-top: 10px;
  margin-bottom: 6px;
  font-size: 14px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  outline: none;
  font-family: inherit;
}

.modal-content input:focus,
.modal-content select:focus {
  border-color: #2563eb;
  box-shadow: 0 0 0 1px #2563eb;
}

/* Labels are hidden so we use placeholders instead (like your design) */

/* Submit button */
.modal-content button[type="submit"] {
  background-color: #2563eb;
  color: white;
  padding: 10px 20px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  margin-top: 16px;
  cursor: pointer;
  float: right;
}

/* Error messages */
.modal-content div[style*="color:red"] {
  font-size: 13px;
  margin-top: -4px;
  margin-bottom: 8px;
}
textarea {
  
  border-radius: 8px;         /* ⬅️ This gives it curved edges */
  border: 1px solid #d1d5db;
  padding: 10px 12px;
  font-size: 14px;
  font-family: inherit;
  outline: none;
  width: 100%;
  resize: vertical;           /* optional: allow resizing vertically only */
}

textarea:focus {
  border-color: #000000;
  box-shadow: 0 0 0 1px #2563eb;
  border: 3px solid black;
}


.modal .form-inputp {
  border: 2px solid #d1d5db;
  border-radius: 8px;
  padding: 10px 12px;
  font-size: 14px;
  width: 100%;
  margin-top: 5px;
  margin-bottom: 10px;
  outline: none;
  font-family: inherit;
}

.modal .form-inputp:focus {
  border-color: #000000;
  box-shadow: 0 0 0 1px #000000;
}

/* Simple Notification Bar */
.simple-notification {
  width: 100%;
  padding: 12px 20px;
  margin-bottom: 15px;
  text-align: center;
  font-weight: 500;
  font-size: 14px;
  border-radius: 8px;
}

.simple-notification.success {
  background-color: #d4edda;
  color: #153257;
  border: 1px solid #c3e6cb;
}

.simple-notification.error {
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

/* ===== AMÉLIORATION TYPOGRAPHIE TABLEAU AFFECTATIONS ===== */

/* Headers du tableau plus grands */
.table thead th {
  font-size: 16px !important;
  font-weight: 600 !important;
  text-transform: capitalize;
  color: #6b7280 !important;
  padding: 16px 8px !important;
  border-bottom: 2px solid #e5e7eb !important;
}

/* Contenu du tableau plus grand */
.table tbody td {
  font-size: 15px !important;
  padding: 16px 8px !important;
  vertical-align: middle !important;
  line-height: 1.5;
  color: #4b5563 !important;
}

/* Noms d'utilisateurs plus grands */
.table .fw-bolder,
.table h6 {
  font-size: 18px !important;
  font-weight: 600 !important;
  text-transform: capitalize;
  color: #374151 !important;
  margin-bottom: 4px !important;
}

/* Informations secondaires plus lisibles */
.table .text-muted,
.table .fs-2 {
  font-size: 14px !important;
  text-transform: capitalize;
  color: #9ca3af !important;
}

/* Départements et autres textes */
.table .fw-medium {
  font-size: 16px !important;
  font-weight: 500 !important;
  text-transform: capitalize;
  color: #6b7280 !important;
}

/* Badges plus grands */
.table .badge {
  font-size: 13px !important;
  font-weight: 500 !important;
  text-transform: capitalize;
  padding: 6px 10px !important;
  border-radius: 6px !important;
}

/* Nombres en gras plus grands */
.table .fw-bold {
  font-size: 17px !important;
  font-weight: 700 !important;
  color: #1e293b !important;
}

/* Informations de contact plus lisibles */
.table .contact-info small {
  font-size: 14px !important;
  line-height: 1.4;
  text-transform: lowercase;
}

.table .contact-info small:first-child {
  font-weight: 500;
  color: #374151 !important;
}

/* Items d'équipement dans le tableau */
.equipment-item {
  background-color: #f8fafc !important;
  border: 1px solid #e2e8f0 !important;
  border-radius: 8px !important;
  padding: 12px !important;
  margin-bottom: 8px !important;
}

.equipment-header {
  font-size: 15px !important;
  font-weight: 600 !important;
  text-transform: capitalize;
  color: #1e293b !important;
}

.equipment-details {
  font-size: 13px !important;
  color: #64748b !important;
  text-transform: capitalize;
}

/* Message "aucun équipement" */
.no-equipment span {
  font-size: 14px !important;
  text-transform: capitalize;
}

/* Boutons d'action plus grands */
.table .btn {
  font-size: 20px !important;
}

/* Amélioration générale du tableau */
.table {
  border-collapse: separate;
  border-spacing: 0;
}

.table tbody tr {
  border-bottom: 1px solid #f1f5f9;
  transition: background-color 0.2s ease;
}

.table tbody tr:hover {
  background-color: #f8fafc;
}
